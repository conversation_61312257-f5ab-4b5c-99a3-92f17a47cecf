import type { DesignSystemConfig, ThemeConfig, ConfigurationManager, RuntimeThemeConfig, ConfigSource } from './types';
import { themes, lightTheme } from './themes';

// Default design system configuration
export const defaultConfig: DesignSystemConfig = {
  themes,
  components: [], // Will be populated with component configurations
  defaultTheme: 'light',
  customizations: {
    enableAnimations: true,
    enableTransitions: true,
    enableShadows: true,
    compactMode: false,
    highContrast: false,
  },
};

// Configuration manager class
class DesignSystemConfigManager implements ConfigurationManager {
  private config: DesignSystemConfig;
  private runtimeConfig: RuntimeThemeConfig;
  private configSources: Map<ConfigSource, Partial<DesignSystemConfig>>;

  constructor(initialConfig: DesignSystemConfig = defaultConfig) {
    this.config = { ...initialConfig };
    this.runtimeConfig = {
      currentTheme: initialConfig.defaultTheme,
    };
    this.configSources = new Map();
    this.loadFromEnvironment();
    this.loadFromLocalStorage();
  }

  getConfig(): DesignSystemConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<DesignSystemConfig>, source: ConfigSource = 'runtime'): void {
    // Store the source of the update
    this.configSources.set(source, updates);
    
    // Merge updates with current config
    this.config = {
      ...this.config,
      ...updates,
      customizations: {
        ...this.config.customizations,
        ...updates.customizations,
      },
    };

    // Apply the configuration
    this.applyConfiguration();
    
    // Save to localStorage if it's a user update
    if (source === 'user' || source === 'runtime') {
      this.saveToLocalStorage();
    }
  }

  getTheme(name: string): ThemeConfig | undefined {
    return this.config.themes.find(theme => theme.name === name);
  }

  getCurrentTheme(): ThemeConfig {
    const theme = this.getTheme(this.runtimeConfig.currentTheme);
    return theme || lightTheme;
  }

  setTheme(name: string): void {
    const theme = this.getTheme(name);
    if (theme) {
      this.runtimeConfig.currentTheme = name;
      this.applyTheme(theme);
      this.saveToLocalStorage();
    }
  }

  resetToDefaults(): void {
    this.config = { ...defaultConfig };
    this.runtimeConfig = {
      currentTheme: defaultConfig.defaultTheme,
    };
    this.configSources.clear();
    this.applyConfiguration();
    localStorage.removeItem('design-system-config');
  }

  // Apply theme CSS variables to the document
  private applyTheme(theme: ThemeConfig): void {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;
    
    // Apply theme CSS variables
    Object.entries(theme.cssVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Apply runtime customizations
    if (this.runtimeConfig.customColors) {
      // Apply custom colors if any
      this.applyCustomColors(this.runtimeConfig.customColors);
    }

    // Apply customization flags
    root.classList.toggle('no-animations', !this.config.customizations.enableAnimations);
    root.classList.toggle('no-transitions', !this.config.customizations.enableTransitions);
    root.classList.toggle('no-shadows', !this.config.customizations.enableShadows);
    root.classList.toggle('compact-mode', this.config.customizations.compactMode);
    root.classList.toggle('high-contrast', this.config.customizations.highContrast);
  }

  private applyCustomColors(customColors: any): void {
    // Implementation for applying custom color overrides
    // This would convert color tokens to CSS variables
  }

  private applyConfiguration(): void {
    const currentTheme = this.getCurrentTheme();
    this.applyTheme(currentTheme);
  }

  private loadFromEnvironment(): void {
    if (typeof process === 'undefined') return;

    const envConfig: Partial<DesignSystemConfig> = {};
    
    // Load theme from environment
    if (process.env.NEXT_PUBLIC_DEFAULT_THEME) {
      envConfig.defaultTheme = process.env.NEXT_PUBLIC_DEFAULT_THEME;
    }

    // Load customizations from environment
    if (process.env.NEXT_PUBLIC_ENABLE_ANIMATIONS === 'false') {
      envConfig.customizations = {
        ...envConfig.customizations,
        enableAnimations: false,
      };
    }

    if (process.env.NEXT_PUBLIC_COMPACT_MODE === 'true') {
      envConfig.customizations = {
        ...envConfig.customizations,
        compactMode: true,
      };
    }

    if (Object.keys(envConfig).length > 0) {
      this.updateConfig(envConfig, 'env');
    }
  }

  private loadFromLocalStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem('design-system-config');
      if (stored) {
        const config = JSON.parse(stored);
        this.updateConfig(config, 'user');
      }
    } catch (error) {
      console.warn('Failed to load design system config from localStorage:', error);
    }
  }

  private saveToLocalStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const configToSave = {
        defaultTheme: this.config.defaultTheme,
        customizations: this.config.customizations,
        runtimeConfig: this.runtimeConfig,
      };
      localStorage.setItem('design-system-config', JSON.stringify(configToSave));
    } catch (error) {
      console.warn('Failed to save design system config to localStorage:', error);
    }
  }

  // Public method to get runtime config
  getRuntimeConfig(): RuntimeThemeConfig {
    return { ...this.runtimeConfig };
  }

  // Public method to update runtime config
  updateRuntimeConfig(updates: Partial<RuntimeThemeConfig>): void {
    this.runtimeConfig = {
      ...this.runtimeConfig,
      ...updates,
    };
    this.applyConfiguration();
    this.saveToLocalStorage();
  }
}

// Create and export the global configuration manager instance
export const configManager = new DesignSystemConfigManager();

// Utility functions for easy access
export const getDesignSystemConfig = () => configManager.getConfig();
export const getCurrentTheme = () => configManager.getCurrentTheme();
export const setTheme = (name: string) => configManager.setTheme(name);
export const updateDesignSystemConfig = (updates: Partial<DesignSystemConfig>, source?: ConfigSource) => 
  configManager.updateConfig(updates, source);

// Hook-like function for React components
export const useDesignSystem = () => ({
  config: configManager.getConfig(),
  currentTheme: configManager.getCurrentTheme(),
  runtimeConfig: configManager.getRuntimeConfig(),
  setTheme: configManager.setTheme.bind(configManager),
  updateConfig: configManager.updateConfig.bind(configManager),
  updateRuntimeConfig: configManager.updateRuntimeConfig.bind(configManager),
  resetToDefaults: configManager.resetToDefaults.bind(configManager),
});

// Initialize the configuration manager
if (typeof window !== 'undefined') {
  // Apply initial configuration on client side
  configManager.setTheme(configManager.getConfig().defaultTheme);
}
