// Design System Type Definitions

export interface DesignTokens {
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  shadows: ShadowTokens;
  borders: BorderTokens;
  animations: AnimationTokens;
}

export interface ColorTokens {
  // Base colors
  primary: ColorScale;
  secondary: ColorScale;
  accent: ColorScale;
  neutral: ColorScale;
  
  // Semantic colors
  success: ColorScale;
  warning: ColorScale;
  error: ColorScale;
  info: ColorScale;
  
  // Surface colors
  background: ColorScale;
  surface: ColorScale;
  overlay: ColorScale;
}

export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}

export interface TypographyTokens {
  fontFamily: {
    sans: string[];
    serif: string[];
    mono: string[];
  };
  fontSize: {
    xs: [string, { lineHeight: string; letterSpacing?: string }];
    sm: [string, { lineHeight: string; letterSpacing?: string }];
    base: [string, { lineHeight: string; letterSpacing?: string }];
    lg: [string, { lineHeight: string; letterSpacing?: string }];
    xl: [string, { lineHeight: string; letterSpacing?: string }];
    '2xl': [string, { lineHeight: string; letterSpacing?: string }];
    '3xl': [string, { lineHeight: string; letterSpacing?: string }];
    '4xl': [string, { lineHeight: string; letterSpacing?: string }];
    '5xl': [string, { lineHeight: string; letterSpacing?: string }];
    '6xl': [string, { lineHeight: string; letterSpacing?: string }];
  };
  fontWeight: {
    thin: string;
    light: string;
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
    extrabold: string;
    black: string;
  };
}

export interface SpacingTokens {
  0: string;
  px: string;
  0.5: string;
  1: string;
  1.5: string;
  2: string;
  2.5: string;
  3: string;
  3.5: string;
  4: string;
  5: string;
  6: string;
  7: string;
  8: string;
  9: string;
  10: string;
  11: string;
  12: string;
  14: string;
  16: string;
  20: string;
  24: string;
  28: string;
  32: string;
  36: string;
  40: string;
  44: string;
  48: string;
  52: string;
  56: string;
  60: string;
  64: string;
  72: string;
  80: string;
  96: string;
}

export interface ShadowTokens {
  sm: string;
  base: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
  none: string;
}

export interface BorderTokens {
  radius: {
    none: string;
    sm: string;
    base: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    full: string;
  };
  width: {
    0: string;
    2: string;
    4: string;
    8: string;
  };
}

export interface AnimationTokens {
  duration: {
    75: string;
    100: string;
    150: string;
    200: string;
    300: string;
    500: string;
    700: string;
    1000: string;
  };
  easing: {
    linear: string;
    in: string;
    out: string;
    'in-out': string;
  };
}

export interface ThemeConfig {
  name: string;
  displayName: string;
  tokens: DesignTokens;
  cssVariables: Record<string, string>;
}

export interface ComponentVariant {
  name: string;
  className: string;
  description?: string;
}

export interface ComponentConfig {
  name: string;
  baseClassName: string;
  variants: {
    [variantName: string]: ComponentVariant[];
  };
  defaultVariants: Record<string, string>;
  compoundVariants?: Array<{
    conditions: Record<string, string>;
    className: string;
  }>;
}

export interface DesignSystemConfig {
  themes: ThemeConfig[];
  components: ComponentConfig[];
  defaultTheme: string;
  customizations: {
    enableAnimations: boolean;
    enableTransitions: boolean;
    enableShadows: boolean;
    compactMode: boolean;
    highContrast: boolean;
  };
}

// Runtime configuration types
export interface RuntimeThemeConfig {
  currentTheme: string;
  customColors?: Partial<ColorTokens>;
  customTypography?: Partial<TypographyTokens>;
  customSpacing?: Partial<SpacingTokens>;
  componentOverrides?: Record<string, Partial<ComponentConfig>>;
}

// Configuration sources
export type ConfigSource = 'default' | 'file' | 'env' | 'runtime' | 'user';

export interface ConfigurationManager {
  getConfig(): DesignSystemConfig;
  updateConfig(updates: Partial<DesignSystemConfig>, source?: ConfigSource): void;
  getTheme(name: string): ThemeConfig | undefined;
  getCurrentTheme(): ThemeConfig;
  setTheme(name: string): void;
  resetToDefaults(): void;
}
