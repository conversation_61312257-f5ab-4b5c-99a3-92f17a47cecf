import type { ThemeConfig } from './types';
import { defaultTokens } from './tokens';

// Light theme configuration
export const lightTheme: ThemeConfig = {
  name: 'light',
  displayName: 'Light',
  tokens: defaultTokens,
  cssVariables: {
    // Base colors
    '--background': '0 0% 100%',
    '--foreground': '240 10% 3.9%',
    '--card': '0 0% 100%',
    '--card-foreground': '240 10% 3.9%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '240 10% 3.9%',
    
    // Primary colors
    '--primary': '240 5.9% 10%',
    '--primary-foreground': '0 0% 98%',
    '--secondary': '240 4.8% 95.9%',
    '--secondary-foreground': '240 5.9% 10%',
    
    // Muted colors
    '--muted': '240 4.8% 95.9%',
    '--muted-foreground': '240 3.8% 46.1%',
    '--accent': '240 4.8% 95.9%',
    '--accent-foreground': '240 5.9% 10%',
    
    // Semantic colors
    '--destructive': '0 84.2% 60.2%',
    '--destructive-foreground': '0 0% 98%',
    '--success': '142 76% 36%',
    '--success-foreground': '0 0% 98%',
    '--warning': '38 92% 50%',
    '--warning-foreground': '0 0% 98%',
    '--info': '221 83% 53%',
    '--info-foreground': '0 0% 98%',
    
    // Border and input
    '--border': '240 5.9% 90%',
    '--input': '240 5.9% 90%',
    '--ring': '240 10% 3.9%',
    
    // Chart colors
    '--chart-1': '12 76% 61%',
    '--chart-2': '173 58% 39%',
    '--chart-3': '197 37% 24%',
    '--chart-4': '43 74% 66%',
    '--chart-5': '27 87% 67%',
    
    // Sidebar colors
    '--sidebar-background': '0 0% 98%',
    '--sidebar-foreground': '240 5.3% 26.1%',
    '--sidebar-primary': '240 5.9% 10%',
    '--sidebar-primary-foreground': '0 0% 98%',
    '--sidebar-accent': '240 4.8% 95.9%',
    '--sidebar-accent-foreground': '240 5.9% 10%',
    '--sidebar-border': '220 13% 91%',
    '--sidebar-ring': '217.2 91.2% 59.8%',
    
    // Design system tokens
    '--radius': '0.5rem',
    '--shadow-sm': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    '--shadow-md': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    '--shadow-lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  },
};

// Dark theme configuration
export const darkTheme: ThemeConfig = {
  name: 'dark',
  displayName: 'Dark',
  tokens: defaultTokens,
  cssVariables: {
    // Base colors
    '--background': '240 10% 3.9%',
    '--foreground': '0 0% 98%',
    '--card': '240 10% 3.9%',
    '--card-foreground': '0 0% 98%',
    '--popover': '240 10% 3.9%',
    '--popover-foreground': '0 0% 98%',
    
    // Primary colors
    '--primary': '0 0% 98%',
    '--primary-foreground': '240 5.9% 10%',
    '--secondary': '240 3.7% 15.9%',
    '--secondary-foreground': '0 0% 98%',
    
    // Muted colors
    '--muted': '240 3.7% 15.9%',
    '--muted-foreground': '240 5% 64.9%',
    '--accent': '240 3.7% 15.9%',
    '--accent-foreground': '0 0% 98%',
    
    // Semantic colors
    '--destructive': '0 62.8% 30.6%',
    '--destructive-foreground': '0 0% 98%',
    '--success': '142 71% 45%',
    '--success-foreground': '0 0% 98%',
    '--warning': '38 92% 50%',
    '--warning-foreground': '0 0% 98%',
    '--info': '221 83% 53%',
    '--info-foreground': '0 0% 98%',
    
    // Border and input
    '--border': '240 3.7% 15.9%',
    '--input': '240 3.7% 15.9%',
    '--ring': '240 4.9% 83.9%',
    
    // Chart colors
    '--chart-1': '220 70% 50%',
    '--chart-2': '160 60% 45%',
    '--chart-3': '30 80% 55%',
    '--chart-4': '280 65% 60%',
    '--chart-5': '340 75% 55%',
    
    // Sidebar colors
    '--sidebar-background': '240 5.9% 10%',
    '--sidebar-foreground': '240 4.8% 95.9%',
    '--sidebar-primary': '224.3 76.3% 48%',
    '--sidebar-primary-foreground': '0 0% 100%',
    '--sidebar-accent': '240 3.7% 15.9%',
    '--sidebar-accent-foreground': '240 4.8% 95.9%',
    '--sidebar-border': '240 3.7% 15.9%',
    '--sidebar-ring': '217.2 91.2% 59.8%',
    
    // Design system tokens
    '--radius': '0.5rem',
    '--shadow-sm': '0 1px 2px 0 rgb(0 0 0 / 0.3)',
    '--shadow-md': '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
    '--shadow-lg': '0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)',
  },
};

// High contrast theme
export const highContrastTheme: ThemeConfig = {
  name: 'high-contrast',
  displayName: 'High Contrast',
  tokens: defaultTokens,
  cssVariables: {
    // Base colors
    '--background': '0 0% 100%',
    '--foreground': '0 0% 0%',
    '--card': '0 0% 100%',
    '--card-foreground': '0 0% 0%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '0 0% 0%',
    
    // Primary colors
    '--primary': '0 0% 0%',
    '--primary-foreground': '0 0% 100%',
    '--secondary': '0 0% 90%',
    '--secondary-foreground': '0 0% 0%',
    
    // Muted colors
    '--muted': '0 0% 85%',
    '--muted-foreground': '0 0% 20%',
    '--accent': '0 0% 85%',
    '--accent-foreground': '0 0% 0%',
    
    // Semantic colors
    '--destructive': '0 100% 40%',
    '--destructive-foreground': '0 0% 100%',
    '--success': '120 100% 25%',
    '--success-foreground': '0 0% 100%',
    '--warning': '45 100% 35%',
    '--warning-foreground': '0 0% 100%',
    '--info': '240 100% 40%',
    '--info-foreground': '0 0% 100%',
    
    // Border and input
    '--border': '0 0% 0%',
    '--input': '0 0% 0%',
    '--ring': '0 0% 0%',
    
    // Chart colors
    '--chart-1': '0 100% 50%',
    '--chart-2': '120 100% 25%',
    '--chart-3': '240 100% 50%',
    '--chart-4': '60 100% 25%',
    '--chart-5': '300 100% 50%',
    
    // Sidebar colors
    '--sidebar-background': '0 0% 95%',
    '--sidebar-foreground': '0 0% 0%',
    '--sidebar-primary': '0 0% 0%',
    '--sidebar-primary-foreground': '0 0% 100%',
    '--sidebar-accent': '0 0% 85%',
    '--sidebar-accent-foreground': '0 0% 0%',
    '--sidebar-border': '0 0% 0%',
    '--sidebar-ring': '0 0% 0%',
    
    // Design system tokens
    '--radius': '0rem',
    '--shadow-sm': '0 2px 4px 0 rgb(0 0 0 / 0.8)',
    '--shadow-md': '0 4px 8px 0 rgb(0 0 0 / 0.8)',
    '--shadow-lg': '0 8px 16px 0 rgb(0 0 0 / 0.8)',
  },
};

// Blue theme
export const blueTheme: ThemeConfig = {
  name: 'blue',
  displayName: 'Ocean Blue',
  tokens: defaultTokens,
  cssVariables: {
    ...lightTheme.cssVariables,
    '--primary': '221 83% 53%',
    '--primary-foreground': '0 0% 98%',
    '--accent': '221 83% 95%',
    '--accent-foreground': '221 83% 20%',
    '--sidebar-primary': '221 83% 53%',
    '--sidebar-accent': '221 83% 95%',
  },
};

// Green theme
export const greenTheme: ThemeConfig = {
  name: 'green',
  displayName: 'Forest Green',
  tokens: defaultTokens,
  cssVariables: {
    ...lightTheme.cssVariables,
    '--primary': '142 76% 36%',
    '--primary-foreground': '0 0% 98%',
    '--accent': '142 76% 95%',
    '--accent-foreground': '142 76% 20%',
    '--sidebar-primary': '142 76% 36%',
    '--sidebar-accent': '142 76% 95%',
  },
};

// Purple theme
export const purpleTheme: ThemeConfig = {
  name: 'purple',
  displayName: 'Royal Purple',
  tokens: defaultTokens,
  cssVariables: {
    ...lightTheme.cssVariables,
    '--primary': '262 83% 58%',
    '--primary-foreground': '0 0% 98%',
    '--accent': '262 83% 95%',
    '--accent-foreground': '262 83% 20%',
    '--sidebar-primary': '262 83% 58%',
    '--sidebar-accent': '262 83% 95%',
  },
};

// Export all themes
export const themes: ThemeConfig[] = [
  lightTheme,
  darkTheme,
  highContrastTheme,
  blueTheme,
  greenTheme,
  purpleTheme,
];

export const getTheme = (name: string): ThemeConfig | undefined => {
  return themes.find(theme => theme.name === name);
};

export const getThemeNames = (): string[] => {
  return themes.map(theme => theme.name);
};
