// Design System TypeScript Interfaces
// Comprehensive type definitions for the design system

import type { VariantProps } from 'class-variance-authority';
import type { buttonVariants } from '@/components/ui/button';
import type { cardVariants } from '@/components/ui/card';

// Base component props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
  'data-testid'?: string;
}

// Button component interfaces
export interface ButtonProps extends BaseComponentProps, VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loadingText?: string;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  form?: string;
  name?: string;
  value?: string;
}

// Card component interfaces
export interface CardProps extends BaseComponentProps, VariantProps<typeof cardVariants> {
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
  role?: string;
  tabIndex?: number;
}

export interface CardHeaderProps extends BaseComponentProps {
  padding?: 'none' | 'sm' | 'default' | 'lg';
}

export interface CardContentProps extends BaseComponentProps {
  padding?: 'none' | 'sm' | 'default' | 'lg';
}

export interface CardFooterProps extends BaseComponentProps {
  padding?: 'none' | 'sm' | 'default' | 'lg';
  justify?: 'start' | 'center' | 'end' | 'between';
}

// Input component interfaces
export interface InputProps extends BaseComponentProps {
  variant?: 'default' | 'filled' | 'outlined' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  state?: 'default' | 'error' | 'success' | 'warning';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  helperText?: string;
  errorText?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  autoComplete?: string;
  autoFocus?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  name?: string;
}

// Theme system interfaces
export interface ThemeContextValue {
  currentTheme: string;
  availableThemes: string[];
  setTheme: (theme: string) => void;
  isDark: boolean;
  isHighContrast: boolean;
  isCompact: boolean;
  toggleDark: () => void;
  toggleHighContrast: () => void;
  toggleCompact: () => void;
}

export interface ThemeSelectorProps extends BaseComponentProps {
  variant?: 'dropdown' | 'compact' | 'grid';
  showPreview?: boolean;
  showSystemOption?: boolean;
}

// Design system configuration interfaces
export interface DesignSystemContextValue {
  config: DesignSystemConfig;
  currentTheme: ThemeConfig;
  runtimeConfig: RuntimeThemeConfig;
  setTheme: (name: string) => void;
  updateConfig: (updates: Partial<DesignSystemConfig>) => void;
  updateRuntimeConfig: (updates: Partial<RuntimeThemeConfig>) => void;
  resetToDefaults: () => void;
}

export interface SettingsPanelProps extends BaseComponentProps {
  variant?: 'full' | 'compact';
  sections?: SettingsSection[];
  onConfigChange?: (config: DesignSystemConfig) => void;
  onThemeChange?: (theme: string) => void;
}

export interface SettingsSection {
  id: string;
  title: string;
  description?: string;
  settings: SettingItem[];
}

export interface SettingItem {
  id: string;
  type: 'boolean' | 'select' | 'range' | 'color' | 'text';
  label: string;
  description?: string;
  value: any;
  options?: Array<{ label: string; value: any }>;
  min?: number;
  max?: number;
  step?: number;
  onChange: (value: any) => void;
}

// Animation and transition interfaces
export interface AnimationProps {
  duration?: 'fast' | 'normal' | 'slow' | number;
  easing?: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  iterationCount?: number | 'infinite';
  playState?: 'running' | 'paused';
}

export interface TransitionProps {
  property?: string | string[];
  duration?: number | string;
  easing?: string;
  delay?: number | string;
}

// Responsive design interfaces
export interface ResponsiveValue<T> {
  base?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}

export interface BreakpointProps {
  hideBelow?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  hideAbove?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  showOnly?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

// Accessibility interfaces
export interface AccessibilityProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  'aria-disabled'?: boolean;
  'aria-pressed'?: boolean;
  'aria-selected'?: boolean;
  'aria-checked'?: boolean;
  'aria-current'?: boolean | 'page' | 'step' | 'location' | 'date' | 'time';
  role?: string;
  tabIndex?: number;
}

// Layout component interfaces
export interface ContainerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  centerContent?: boolean;
  padding?: ResponsiveValue<'none' | 'sm' | 'md' | 'lg'>;
}

export interface StackProps extends BaseComponentProps {
  direction?: ResponsiveValue<'row' | 'column'>;
  spacing?: ResponsiveValue<number | string>;
  align?: ResponsiveValue<'start' | 'center' | 'end' | 'stretch'>;
  justify?: ResponsiveValue<'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'>;
  wrap?: ResponsiveValue<boolean>;
}

export interface GridProps extends BaseComponentProps {
  columns?: ResponsiveValue<number>;
  rows?: ResponsiveValue<number>;
  gap?: ResponsiveValue<number | string>;
  columnGap?: ResponsiveValue<number | string>;
  rowGap?: ResponsiveValue<number | string>;
  autoFlow?: 'row' | 'column' | 'dense' | 'row dense' | 'column dense';
  autoColumns?: string;
  autoRows?: string;
}

// Form component interfaces
export interface FormFieldProps extends BaseComponentProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  orientation?: 'vertical' | 'horizontal';
}

export interface FormProps extends BaseComponentProps {
  onSubmit?: (event: React.FormEvent<HTMLFormElement>) => void;
  noValidate?: boolean;
  autoComplete?: 'on' | 'off';
}

// Utility type helpers
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type ComponentVariant = 'default' | 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
export type ComponentState = 'default' | 'hover' | 'active' | 'focus' | 'disabled' | 'loading';
export type ColorScheme = 'light' | 'dark' | 'auto';
export type SemanticColor = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';

// Event handler types
export type ClickHandler = (event: React.MouseEvent) => void;
export type KeyboardHandler = (event: React.KeyboardEvent) => void;
export type FocusHandler = (event: React.FocusEvent) => void;
export type ChangeHandler<T = string> = (value: T) => void;

// Design token value types
export type SpacingValue = keyof typeof import('./tokens').defaultTokens.spacing;
export type ColorValue = keyof typeof import('./tokens').defaultTokens.colors;
export type FontSizeValue = keyof typeof import('./tokens').defaultTokens.typography.fontSize;
export type FontWeightValue = keyof typeof import('./tokens').defaultTokens.typography.fontWeight;
export type BorderRadiusValue = keyof typeof import('./tokens').defaultTokens.borders.radius;
export type ShadowValue = keyof typeof import('./tokens').defaultTokens.shadows;

// Component composition helpers
export interface AsChildProps {
  asChild?: boolean;
}

export interface PolymorphicProps<T extends React.ElementType = React.ElementType> {
  as?: T;
}

export type PolymorphicComponentProps<T extends React.ElementType, P = {}> = P &
  PolymorphicProps<T> &
  Omit<React.ComponentPropsWithoutRef<T>, keyof P | 'as'>;

// Export commonly used types
export type {
  DesignSystemConfig,
  ThemeConfig,
  RuntimeThemeConfig,
  ComponentConfig,
  DesignTokens,
  ColorTokens,
  TypographyTokens,
  SpacingTokens,
  ShadowTokens,
  BorderTokens,
  AnimationTokens,
} from './types';
