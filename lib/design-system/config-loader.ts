import type { DesignSystemConfig } from './types';
import { defaultConfig } from './config';

// Configuration file loader
export async function loadConfigFromFile(path?: string): Promise<Partial<DesignSystemConfig> | null> {
  try {
    const configPath = path || '/design-system.config.json';
    const response = await fetch(configPath);
    
    if (!response.ok) {
      console.warn(`Design system config file not found at ${configPath}`);
      return null;
    }
    
    const config = await response.json();
    return validateConfig(config);
  } catch (error) {
    console.warn('Failed to load design system config file:', error);
    return null;
  }
}

// Environment variable loader
export function loadConfigFromEnv(): Partial<DesignSystemConfig> {
  const envConfig: Partial<DesignSystemConfig> = {};
  
  // Theme configuration
  if (process.env.NEXT_PUBLIC_DEFAULT_THEME) {
    envConfig.defaultTheme = process.env.NEXT_PUBLIC_DEFAULT_THEME;
  }
  
  // Customization flags
  const customizations: any = {};
  
  if (process.env.NEXT_PUBLIC_ENABLE_ANIMATIONS !== undefined) {
    customizations.enableAnimations = process.env.NEXT_PUBLIC_ENABLE_ANIMATIONS === 'true';
  }
  
  if (process.env.NEXT_PUBLIC_ENABLE_TRANSITIONS !== undefined) {
    customizations.enableTransitions = process.env.NEXT_PUBLIC_ENABLE_TRANSITIONS === 'true';
  }
  
  if (process.env.NEXT_PUBLIC_ENABLE_SHADOWS !== undefined) {
    customizations.enableShadows = process.env.NEXT_PUBLIC_ENABLE_SHADOWS === 'true';
  }
  
  if (process.env.NEXT_PUBLIC_COMPACT_MODE !== undefined) {
    customizations.compactMode = process.env.NEXT_PUBLIC_COMPACT_MODE === 'true';
  }
  
  if (process.env.NEXT_PUBLIC_HIGH_CONTRAST !== undefined) {
    customizations.highContrast = process.env.NEXT_PUBLIC_HIGH_CONTRAST === 'true';
  }
  
  if (Object.keys(customizations).length > 0) {
    envConfig.customizations = customizations;
  }
  
  return envConfig;
}

// URL parameter loader
export function loadConfigFromURL(): Partial<DesignSystemConfig> {
  if (typeof window === 'undefined') return {};
  
  const urlParams = new URLSearchParams(window.location.search);
  const urlConfig: Partial<DesignSystemConfig> = {};
  
  // Theme from URL
  const theme = urlParams.get('theme');
  if (theme) {
    urlConfig.defaultTheme = theme;
  }
  
  // Customizations from URL
  const customizations: any = {};
  
  if (urlParams.has('compact')) {
    customizations.compactMode = urlParams.get('compact') === 'true';
  }
  
  if (urlParams.has('animations')) {
    customizations.enableAnimations = urlParams.get('animations') === 'true';
  }
  
  if (urlParams.has('high-contrast')) {
    customizations.highContrast = urlParams.get('high-contrast') === 'true';
  }
  
  if (Object.keys(customizations).length > 0) {
    urlConfig.customizations = customizations;
  }
  
  return urlConfig;
}

// Configuration validator
function validateConfig(config: any): Partial<DesignSystemConfig> {
  const validatedConfig: Partial<DesignSystemConfig> = {};
  
  // Validate theme
  if (config.defaultTheme && typeof config.defaultTheme === 'string') {
    validatedConfig.defaultTheme = config.defaultTheme;
  }
  
  // Validate customizations
  if (config.customizations && typeof config.customizations === 'object') {
    const customizations: any = {};
    
    if (typeof config.customizations.enableAnimations === 'boolean') {
      customizations.enableAnimations = config.customizations.enableAnimations;
    }
    
    if (typeof config.customizations.enableTransitions === 'boolean') {
      customizations.enableTransitions = config.customizations.enableTransitions;
    }
    
    if (typeof config.customizations.enableShadows === 'boolean') {
      customizations.enableShadows = config.customizations.enableShadows;
    }
    
    if (typeof config.customizations.compactMode === 'boolean') {
      customizations.compactMode = config.customizations.compactMode;
    }
    
    if (typeof config.customizations.highContrast === 'boolean') {
      customizations.highContrast = config.customizations.highContrast;
    }
    
    if (Object.keys(customizations).length > 0) {
      validatedConfig.customizations = customizations;
    }
  }
  
  return validatedConfig;
}

// Merge configurations with priority order
export function mergeConfigs(...configs: Array<Partial<DesignSystemConfig> | null>): DesignSystemConfig {
  let mergedConfig = { ...defaultConfig };
  
  for (const config of configs) {
    if (config) {
      mergedConfig = {
        ...mergedConfig,
        ...config,
        customizations: {
          ...mergedConfig.customizations,
          ...config.customizations,
        },
      };
    }
  }
  
  return mergedConfig;
}

// Load configuration from all sources
export async function loadFullConfiguration(): Promise<DesignSystemConfig> {
  const [fileConfig] = await Promise.all([
    loadConfigFromFile(),
  ]);
  
  const envConfig = loadConfigFromEnv();
  const urlConfig = loadConfigFromURL();
  
  // Priority order: URL > Environment > File > Default
  return mergeConfigs(defaultConfig, fileConfig, envConfig, urlConfig);
}

// Configuration change listener
export class ConfigurationWatcher {
  private listeners: Array<(config: DesignSystemConfig) => void> = [];
  private currentConfig: DesignSystemConfig;
  
  constructor(initialConfig: DesignSystemConfig) {
    this.currentConfig = initialConfig;
    this.setupWatchers();
  }
  
  subscribe(listener: (config: DesignSystemConfig) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }
  
  updateConfig(newConfig: DesignSystemConfig): void {
    this.currentConfig = newConfig;
    this.notifyListeners();
  }
  
  getCurrentConfig(): DesignSystemConfig {
    return this.currentConfig;
  }
  
  private setupWatchers(): void {
    // Watch for URL changes
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', () => {
        this.handleConfigChange();
      });
      
      // Watch for localStorage changes
      window.addEventListener('storage', (event) => {
        if (event.key === 'design-system-config') {
          this.handleConfigChange();
        }
      });
    }
  }
  
  private async handleConfigChange(): Promise<void> {
    const newConfig = await loadFullConfiguration();
    if (JSON.stringify(newConfig) !== JSON.stringify(this.currentConfig)) {
      this.updateConfig(newConfig);
    }
  }
  
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.currentConfig);
      } catch (error) {
        console.error('Error in configuration change listener:', error);
      }
    });
  }
}

// Export utilities
export const createConfigWatcher = (config: DesignSystemConfig) => new ConfigurationWatcher(config);
