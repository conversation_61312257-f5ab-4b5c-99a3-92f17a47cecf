/**
 * AI Provider Configuration
 *
 * This file manages API keys and configuration for different AI providers.
 * Make sure to set the appropriate environment variables for each provider you want to use.
 */

// Validate required environment variables
function validateApi<PERSON>ey(key: string | undefined, providerName: string): string {
  if (!key) {
    console.warn(
      `Warning: ${providerName} API key not found. ${providerName} models will not be available.`,
    );
    return '';
  }
  return key;
}

// API Keys Configuration
export const apiKeys = {
  xai: validateApi<PERSON>ey(process.env.XAI_API_KEY, 'xAI'),
  google: validate<PERSON><PERSON><PERSON>ey(process.env.GOOGLE_GENERATIVE_AI_API_KEY, 'Google'),
  mistral: validate<PERSON>pi<PERSON>ey(process.env.MISTRAL_API_KEY, 'Mistral'),
} as const;

// Provider availability check
export const providerAvailability = {
  xai: !!apiKeys.xai,
  google: !!apiKeys.google,
  mistral: !!apiKeys.mistral,
} as const;

// Model configurations with provider information
export const modelConfigs = {
  // xAI Models
  'chat-model': {
    provider: 'xai',
    modelId: 'grok-2-vision-1212',
    available: providerAvailability.xai,
  },
  'chat-model-reasoning': {
    provider: 'xai',
    modelId: 'grok-3-mini-beta',
    available: providerAvailability.xai,
  },

  // Google Models
  'google-gemini-2-flash': {
    provider: 'google',
    modelId: 'gemini-2.0-flash-exp',
    available: providerAvailability.google,
  },
  'google-gemini-pro': {
    provider: 'google',
    modelId: 'gemini-1.5-pro-latest',
    available: providerAvailability.google,
  },
  'google-gemini-flash': {
    provider: 'google',
    modelId: 'gemini-1.5-flash-latest',
    available: providerAvailability.google,
  },
  'google-gemini-flash-8b': {
    provider: 'google',
    modelId: 'gemini-1.5-flash-8b-latest',
    available: providerAvailability.google,
  },

  // Mistral Models
  'mistral-large': {
    provider: 'mistral',
    modelId: 'mistral-large-latest',
    available: providerAvailability.mistral,
  },
  'mistral-small': {
    provider: 'mistral',
    modelId: 'mistral-small-latest',
    available: providerAvailability.mistral,
  },
  'mistral-codestral': {
    provider: 'mistral',
    modelId: 'codestral-latest',
    available: providerAvailability.mistral,
  },
  'mistral-pixtral': {
    provider: 'mistral',
    modelId: 'pixtral-12b-2409',
    available: providerAvailability.mistral,
  },
} as const;

// Get available models based on API key availability
export function getAvailableModels() {
  return Object.entries(modelConfigs)
    .filter(([_, config]) => config.available)
    .map(([modelId]) => modelId);
}

// Check if a specific model is available
export function isModelAvailable(modelId: string): boolean {
  const config = modelConfigs[modelId as keyof typeof modelConfigs];
  return config?.available ?? false;
}

// Get fallback model if the requested model is not available
export function getFallbackModel(requestedModel: string): string {
  if (isModelAvailable(requestedModel)) {
    return requestedModel;
  }

  // Fallback priority: Google -> xAI -> Mistral
  const fallbackOrder = [
    'google-gemini-2-flash',
    'google-gemini-flash',
    'google-gemini-flash-8b',
    'chat-model',
    'mistral-small',
  ];

  for (const fallback of fallbackOrder) {
    if (isModelAvailable(fallback)) {
      console.warn(
        `Model ${requestedModel} not available, falling back to ${fallback}`,
      );
      return fallback;
    }
  }

  throw new Error(
    'No AI models are available. Please check your API key configuration.',
  );
}
