import type { UserType } from '@/app/(auth)/auth';
import type { ChatModel } from './models';
import { isModelAvailable } from './config';

interface Entitlements {
  maxMessagesPerDay: number;
  availableChatModelIds: Array<ChatModel['id']>;
}

// Define base entitlements without filtering
const baseEntitlementsByUserType: Record<UserType, Entitlements> = {
  /*
   * For users without an account - Limited access to basic models
   */
  guest: {
    maxMessagesPerDay: 20,
    availableChatModelIds: [
      'google-gemini-2-flash',
      'google-gemini-flash-8b',
      'chat-model',
      'mistral-small',
    ],
  },

  /*
   * For users with an account - Access to all models
   */
  regular: {
    maxMessagesPerDay: 100,
    availableChatModelIds: [
      // Google Models (Prioritized)
      'google-gemini-2-flash',
      'google-gemini-pro',
      'google-gemini-flash',
      'google-gemini-flash-8b',

      // xAI Models
      'chat-model',
      'chat-model-reasoning',

      // Mistral Models
      'mistral-large',
      'mistral-small',
      'mistral-codestral',
      'mistral-pixtral',
    ],
  },

  /*
   * TODO: For users with an account and a paid membership
   */
};

// Export entitlements without API key filtering - UI shows all entitled models
// API key availability is checked at runtime in the model selector component
export const entitlementsByUserType: Record<UserType, Entitlements> =
  baseEntitlementsByUserType;

// Export runtime-filtered entitlements for actual model usage
export const runtimeEntitlementsByUserType: Record<UserType, Entitlements> =
  Object.fromEntries(
    Object.entries(baseEntitlementsByUserType).map(
      ([userType, entitlements]) => [
        userType,
        {
          ...entitlements,
          availableChatModelIds:
            entitlements.availableChatModelIds.filter(isModelAvailable),
        },
      ],
    ),
  ) as Record<UserType, Entitlements>;
