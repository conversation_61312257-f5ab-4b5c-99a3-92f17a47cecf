import type { UserType } from '@/app/(auth)/auth';
import type { ChatModel } from './models';
import { isModelAvailable } from './config';

interface Entitlements {
  maxMessagesPerDay: number;
  availableChatModelIds: Array<ChatModel['id']>;
}

// Define base entitlements without filtering
const baseEntitlementsByUserType: Record<UserType, Entitlements> = {
  /*
   * For users without an account - Limited access to basic models
   */
  guest: {
    maxMessagesPerDay: 20,
    availableChatModelIds: [
      'chat-model',
      'google-gemini-flash-8b',
      'mistral-small',
    ],
  },

  /*
   * For users with an account - Access to all models
   */
  regular: {
    maxMessagesPerDay: 100,
    availableChatModelIds: [
      // xAI Models
      'chat-model',
      'chat-model-reasoning',

      // Google Models
      'google-gemini-pro',
      'google-gemini-flash',
      'google-gemini-flash-8b',

      // Mistral Models
      'mistral-large',
      'mistral-small',
      'mistral-codestral',
      'mistral-pixtral',
    ],
  },

  /*
   * TODO: For users with an account and a paid membership
   */
};

// Filter entitlements based on available models (API keys)
export const entitlementsByUserType: Record<UserType, Entitlements> = Object.fromEntries(
  Object.entries(baseEntitlementsByUserType).map(([userType, entitlements]) => [
    userType,
    {
      ...entitlements,
      availableChatModelIds: entitlements.availableChatModelIds.filter(isModelAvailable),
    },
  ])
) as Record<UserType, Entitlements>;
