import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { xai } from '@ai-sdk/xai';
import { google } from '@ai-sdk/google';
import { mistral } from '@ai-sdk/mistral';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from './models.test';
import { isTestEnvironment } from '../constants';
import { apiKeys, providerAvailability } from './config';

// Create language models object conditionally based on API key availability
const createLanguageModels = () => {
  const models: Record<string, any> = {};

  // xAI Models (if API key is available)
  if (providerAvailability.xai) {
    models['chat-model'] = xai('grok-2-vision-1212');
    models['chat-model-reasoning'] = wrapLanguageModel({
      model: xai('grok-3-mini-beta'),
      middleware: extractReasoningMiddleware({ tagName: 'think' }),
    });
    models['title-model'] = xai('grok-2-1212');
    models['artifact-model'] = xai('grok-2-1212');
  }

  // Google Models (if API key is available)
  if (providerAvailability.google) {
    models['google-gemini-pro'] = google('gemini-1.5-pro-latest');
    models['google-gemini-flash'] = google('gemini-1.5-flash-latest');
    models['google-gemini-flash-8b'] = google('gemini-1.5-flash-8b-latest');
  }

  // Mistral Models (if API key is available)
  if (providerAvailability.mistral) {
    models['mistral-large'] = mistral('mistral-large-latest');
    models['mistral-small'] = mistral('mistral-small-latest');
    models['mistral-codestral'] = mistral('codestral-latest');
    models['mistral-pixtral'] = mistral('pixtral-12b-2409');
  }

  return models;
};

// Create image models object conditionally
const createImageModels = () => {
  const models: Record<string, any> = {};

  if (providerAvailability.xai) {
    models['small-model'] = xai.imageModel('grok-2-image');
  }

  return models;
};

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
      },
    })
  : customProvider({
      languageModels: createLanguageModels(),
      imageModels: createImageModels(),
    });
