import { isModelAvailable, getFallbackModel } from './config';

export const DEFAULT_CHAT_MODEL: string = 'google-gemini-2-flash';

export interface ChatModel {
  id: string;
  name: string;
  description: string;
  provider: 'xai' | 'google' | 'mistral';
}

const allChatModels: Array<ChatModel> = [
  // Google Models (Prioritized)
  {
    id: 'google-gemini-2-flash',
    name: 'Gemini 2.0 Flash',
    description:
      "Google's latest and fastest multimodal model with enhanced capabilities",
    provider: 'google',
  },
  {
    id: 'google-gemini-pro',
    name: 'Gemini 1.5 Pro',
    description: "Google's most capable model for complex tasks",
    provider: 'google',
  },
  {
    id: 'google-gemini-flash',
    name: 'Gemini 1.5 Flash',
    description: "Google's fast and efficient model for quick responses",
    provider: 'google',
  },
  {
    id: 'google-gemini-flash-8b',
    name: 'Gemini 1.5 Flash 8B',
    description: "Google's lightweight model for simple tasks",
    provider: 'google',
  },

  // xAI Models
  {
    id: 'chat-model',
    name: 'Grok 2 Vision',
    description: "xAI's multimodal model with vision capabilities",
    provider: 'xai',
  },
  {
    id: 'chat-model-reasoning',
    name: 'Grok 3 Mini (Reasoning)',
    description: "xAI's reasoning model with advanced problem-solving",
    provider: 'xai',
  },

  // Mistral Models
  {
    id: 'mistral-large',
    name: 'Mistral Large',
    description: "Mistral's most powerful model for complex reasoning",
    provider: 'mistral',
  },
  {
    id: 'mistral-small',
    name: 'Mistral Small',
    description: "Mistral's efficient model for everyday tasks",
    provider: 'mistral',
  },
  {
    id: 'mistral-codestral',
    name: 'Codestral',
    description: "Mistral's specialized model for code generation",
    provider: 'mistral',
  },
  {
    id: 'mistral-pixtral',
    name: 'Pixtral 12B',
    description: "Mistral's multimodal model with vision capabilities",
    provider: 'mistral',
  },
];

// Export only available models based on API key configuration
export const chatModels: Array<ChatModel> = allChatModels.filter((model) =>
  isModelAvailable(model.id),
);

// Get default model with fallback logic
export function getDefaultChatModel(): string {
  return getFallbackModel(DEFAULT_CHAT_MODEL);
}
