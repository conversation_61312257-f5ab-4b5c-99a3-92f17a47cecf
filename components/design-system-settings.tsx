'use client';

import { useState } from 'react';
import { useDesignSystemContext } from './theme-provider';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { Separator } from './ui/separator';
import { Switch } from './ui/switch';
import { Slider } from './ui/slider';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { SettingsIcon, RefreshCwIcon } from './icons';
import { ThemePreview } from './theme-selector';
import { themes } from '@/lib/design-system/themes';

export function DesignSystemSettings() {
  const { config, updateConfig, resetToDefaults } = useDesignSystemContext();
  const [isOpen, setIsOpen] = useState(false);

  const handleCustomizationChange = (key: keyof typeof config.customizations, value: boolean) => {
    updateConfig({
      customizations: {
        ...config.customizations,
        [key]: value,
      },
    });
  };

  const handleReset = () => {
    resetToDefaults();
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <SettingsIcon className="h-4 w-4" />
          <span className="sr-only">Design system settings</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Design System Settings</DialogTitle>
          <DialogDescription>
            Customize the appearance and behavior of the interface.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Theme Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Theme Selection</CardTitle>
              <CardDescription>
                Choose from available color themes
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {themes.map((theme) => (
                  <ThemePreview key={theme.name} themeName={theme.name} />
                ))}
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Visual Customizations */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Visual Effects</CardTitle>
              <CardDescription>
                Control animations, transitions, and visual effects
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="animations">Enable Animations</Label>
                  <p className="text-sm text-muted-foreground">
                    Turn on/off smooth animations throughout the interface
                  </p>
                </div>
                <Switch
                  id="animations"
                  checked={config.customizations.enableAnimations}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('enableAnimations', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="transitions">Enable Transitions</Label>
                  <p className="text-sm text-muted-foreground">
                    Smooth transitions between states and pages
                  </p>
                </div>
                <Switch
                  id="transitions"
                  checked={config.customizations.enableTransitions}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('enableTransitions', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="shadows">Enable Shadows</Label>
                  <p className="text-sm text-muted-foreground">
                    Add depth with drop shadows on components
                  </p>
                </div>
                <Switch
                  id="shadows"
                  checked={config.customizations.enableShadows}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('enableShadows', checked)
                  }
                />
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Layout Customizations */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Layout Options</CardTitle>
              <CardDescription>
                Adjust spacing and layout preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="compact">Compact Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Reduce spacing for a more compact interface
                  </p>
                </div>
                <Switch
                  id="compact"
                  checked={config.customizations.compactMode}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('compactMode', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="high-contrast">High Contrast</Label>
                  <p className="text-sm text-muted-foreground">
                    Increase contrast for better accessibility
                  </p>
                </div>
                <Switch
                  id="high-contrast"
                  checked={config.customizations.highContrast}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('highContrast', checked)
                  }
                />
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Reset Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Reset Settings</CardTitle>
              <CardDescription>
                Restore all settings to their default values
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                variant="outline" 
                onClick={handleReset}
                className="w-full"
              >
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Reset to Defaults
              </Button>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Compact settings for mobile/sidebar
export function CompactDesignSystemSettings() {
  const { config, updateConfig } = useDesignSystemContext();

  return (
    <div className="space-y-4 p-4">
      <h3 className="font-medium">Quick Settings</h3>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label htmlFor="compact-animations" className="text-sm">
            Animations
          </Label>
          <Switch
            id="compact-animations"
            size="sm"
            checked={config.customizations.enableAnimations}
            onCheckedChange={(checked) => 
              updateConfig({
                customizations: {
                  ...config.customizations,
                  enableAnimations: checked,
                },
              })
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="compact-compact" className="text-sm">
            Compact
          </Label>
          <Switch
            id="compact-compact"
            size="sm"
            checked={config.customizations.compactMode}
            onCheckedChange={(checked) => 
              updateConfig({
                customizations: {
                  ...config.customizations,
                  compactMode: checked,
                },
              })
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <Label htmlFor="compact-contrast" className="text-sm">
            High Contrast
          </Label>
          <Switch
            id="compact-contrast"
            size="sm"
            checked={config.customizations.highContrast}
            onCheckedChange={(checked) => 
              updateConfig({
                customizations: {
                  ...config.customizations,
                  highContrast: checked,
                },
              })
            }
          />
        </div>
      </div>
    </div>
  );
}
