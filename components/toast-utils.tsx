'use client';

import { toast as sonnerToast } from 'sonner';

export interface ToastOptions {
  type?: 'success' | 'error' | 'warning' | 'info' | 'default';
  title?: string;
  description: string;
  duration?: number;
}

export function toast({ type = 'default', title, description, duration }: ToastOptions) {
  switch (type) {
    case 'success':
      return sonnerToast.success(title || 'Success', {
        description,
        duration,
      });
    case 'error':
      return sonnerToast.error(title || 'Error', {
        description,
        duration,
      });
    case 'warning':
      return sonnerToast.warning(title || 'Warning', {
        description,
        duration,
      });
    case 'info':
      return sonnerToast.info(title || 'Info', {
        description,
        duration,
      });
    default:
      return sonnerToast(title || description, {
        description: title ? description : undefined,
        duration,
      });
  }
}

// Convenience methods
export const toastSuccess = (description: string, title?: string) =>
  toast({ type: 'success', title, description });

export const toastError = (description: string, title?: string) =>
  toast({ type: 'error', title, description });

export const toastWarning = (description: string, title?: string) =>
  toast({ type: 'warning', title, description });

export const toastInfo = (description: string, title?: string) =>
  toast({ type: 'info', title, description });

// Hook for using toasts in components
export function useToast() {
  return {
    toast,
    success: toastSuccess,
    error: toastError,
    warning: toastWarning,
    info: toastInfo,
  };
}
