'use client';

import { startTransition, useMemo, useOptimistic, useState } from 'react';

import { saveChatModelAsCookie } from '@/app/(chat)/actions';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { chatModels } from '@/lib/ai/models';
import { isModelAvailable } from '@/lib/ai/config';
import { cn } from '@/lib/utils';

import { CheckCircleFillIcon, ChevronDownIcon } from './icons';
import { entitlementsByUserType } from '@/lib/ai/entitlements';
import type { Session } from 'next-auth';
import { Badge } from '@/components/ui/badge';

export function ModelSelector({
  session,
  selectedModelId,
  className,
}: {
  session: Session;
  selectedModelId: string;
} & React.ComponentProps<typeof Button>) {
  const [open, setOpen] = useState(false);
  const [optimisticModelId, setOptimisticModelId] =
    useOptimistic(selectedModelId);

  const userType = session.user.type;
  const { availableChatModelIds } = entitlementsByUserType[userType];

  const availableChatModels = chatModels.filter((chatModel) =>
    availableChatModelIds.includes(chatModel.id),
  );

  // Group models by provider for better organization
  const groupedModels = availableChatModels.reduce(
    (acc, model) => {
      if (!acc[model.provider]) {
        acc[model.provider] = [];
      }
      acc[model.provider].push(model);
      return acc;
    },
    {} as Record<string, typeof availableChatModels>,
  );

  // Provider display names and colors
  const providerInfo = {
    google: {
      name: 'Google',
      color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    },
    xai: {
      name: 'xAI',
      color:
        'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    },
    mistral: {
      name: 'Mistral',
      color:
        'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    },
  };

  const selectedChatModel = useMemo(
    () =>
      availableChatModels.find(
        (chatModel) => chatModel.id === optimisticModelId,
      ),
    [optimisticModelId, availableChatModels],
  );

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        className={cn(
          'w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground',
          className,
        )}
      >
        <Button
          data-testid="model-selector"
          variant="outline"
          className="md:px-2 md:h-[34px]"
        >
          {selectedChatModel?.name || 'Select Model'}
          <ChevronDownIcon />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="min-w-[350px] max-h-[400px] overflow-y-auto"
      >
        {Object.entries(groupedModels).map(([provider, models]) => (
          <div key={provider} className="py-2">
            <div className="px-2 py-1 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              {providerInfo[provider as keyof typeof providerInfo]?.name ||
                provider}
            </div>
            {models.map((chatModel) => {
              const { id } = chatModel;
              const isAvailable = isModelAvailable(id);

              return (
                <DropdownMenuItem
                  data-testid={`model-selector-item-${id}`}
                  key={id}
                  onSelect={() => {
                    setOpen(false);

                    startTransition(() => {
                      setOptimisticModelId(id);
                      saveChatModelAsCookie(id);
                    });
                  }}
                  data-active={id === optimisticModelId}
                  disabled={!isAvailable}
                  asChild
                >
                  <button
                    type="button"
                    className={cn(
                      'gap-4 group/item flex flex-row justify-between items-center w-full p-2',
                      !isAvailable && 'opacity-50 cursor-not-allowed',
                    )}
                  >
                    <div className="flex flex-col gap-1 items-start flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{chatModel.name}</span>
                        <Badge
                          variant="secondary"
                          className={cn(
                            'text-xs px-1.5 py-0.5',
                            providerInfo[
                              chatModel.provider as keyof typeof providerInfo
                            ]?.color,
                          )}
                        >
                          {providerInfo[
                            chatModel.provider as keyof typeof providerInfo
                          ]?.name || chatModel.provider}
                        </Badge>
                        {!isAvailable && (
                          <Badge
                            variant="outline"
                            className="text-xs px-1.5 py-0.5 text-muted-foreground"
                          >
                            API Key Required
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground text-left">
                        {chatModel.description}
                        {!isAvailable && (
                          <span className="block mt-1 text-xs text-amber-600 dark:text-amber-400">
                            Configure{' '}
                            {
                              providerInfo[
                                chatModel.provider as keyof typeof providerInfo
                              ]?.name
                            }{' '}
                            API key to use this model
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100">
                      <CheckCircleFillIcon />
                    </div>
                  </button>
                </DropdownMenuItem>
              );
            })}
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
