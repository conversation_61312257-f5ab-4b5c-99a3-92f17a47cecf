'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import type { ThemeProviderProps } from 'next-themes/dist/types';
import { createContext, useContext, useEffect, useState } from 'react';
import { useDesignSystem } from '@/lib/design-system/config';
import type {
  ThemeConfig,
  DesignSystemConfig,
} from '@/lib/design-system/types';

// Design System Context
interface DesignSystemContextType {
  config: DesignSystemConfig;
  currentTheme: ThemeConfig;
  setTheme: (name: string) => void;
  updateConfig: (updates: Partial<DesignSystemConfig>) => void;
  resetToDefaults: () => void;
}

const DesignSystemContext = createContext<DesignSystemContextType | undefined>(
  undefined,
);

export function useDesignSystemContext() {
  const context = useContext(DesignSystemContext);
  if (context === undefined) {
    throw new Error(
      'useDesignSystemContext must be used within a DesignSystemProvider',
    );
  }
  return context;
}

// Enhanced Theme Provider with Design System integration
export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const [mounted, setMounted] = useState(false);
  const designSystem = useDesignSystem();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return null;
  }

  return (
    <NextThemesProvider {...props}>
      <DesignSystemContext.Provider value={designSystem}>
        {children}
      </DesignSystemContext.Provider>
    </NextThemesProvider>
  );
}
