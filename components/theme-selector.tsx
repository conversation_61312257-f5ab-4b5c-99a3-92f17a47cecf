'use client';

import { useTheme } from 'next-themes';
import { useDesignSystemContext } from './theme-provider';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { PaletteIcon, CheckIcon } from './icons';
import { themes } from '@/lib/design-system/themes';

export function ThemeSelector() {
  const { theme, setTheme } = useTheme();
  const { currentTheme, setTheme: setDesignTheme } = useDesignSystemContext();

  const handleThemeChange = (themeName: string) => {
    // Update both next-themes and design system theme
    setTheme(themeName === 'system' ? 'system' : themeName);
    if (themeName !== 'system') {
      setDesignTheme(themeName);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <PaletteIcon className="h-4 w-4" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Choose Theme</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* System theme option */}
        <DropdownMenuItem onClick={() => handleThemeChange('system')}>
          <div className="flex items-center justify-between w-full">
            <span>System</span>
            {theme === 'system' && <CheckIcon className="h-4 w-4" />}
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        {/* Design system themes */}
        {themes.map((themeConfig) => (
          <DropdownMenuItem
            key={themeConfig.name}
            onClick={() => handleThemeChange(themeConfig.name)}
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded-full border border-border"
                  style={{
                    backgroundColor: `hsl(${themeConfig.cssVariables['--primary']})`,
                  }}
                />
                <span>{themeConfig.displayName}</span>
              </div>
              {currentTheme.name === themeConfig.name && (
                <CheckIcon className="h-4 w-4" />
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact theme selector for mobile
export function CompactThemeSelector() {
  const { theme, setTheme } = useTheme();
  const { currentTheme, setTheme: setDesignTheme } = useDesignSystemContext();

  const handleThemeChange = (themeName: string) => {
    setTheme(themeName === 'system' ? 'system' : themeName);
    if (themeName !== 'system') {
      setDesignTheme(themeName);
    }
  };

  return (
    <div className="flex flex-wrap gap-2 p-2">
      <Button
        variant={theme === 'system' ? 'default' : 'outline'}
        size="sm"
        onClick={() => handleThemeChange('system')}
        className="text-xs"
      >
        System
      </Button>
      {themes.slice(0, 4).map((themeConfig) => (
        <Button
          key={themeConfig.name}
          variant={currentTheme.name === themeConfig.name ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleThemeChange(themeConfig.name)}
          className="text-xs"
        >
          <div
            className="w-3 h-3 rounded-full mr-1"
            style={{
              backgroundColor: `hsl(${themeConfig.cssVariables['--primary']})`,
            }}
          />
          {themeConfig.displayName}
        </Button>
      ))}
    </div>
  );
}

// Theme preview component
export function ThemePreview({ themeName }: { themeName: string }) {
  const themeConfig = themes.find(t => t.name === themeName);
  
  if (!themeConfig) return null;

  return (
    <div className="p-4 border rounded-lg space-y-3">
      <div className="flex items-center gap-2">
        <div
          className="w-6 h-6 rounded-full border"
          style={{
            backgroundColor: `hsl(${themeConfig.cssVariables['--primary']})`,
          }}
        />
        <h3 className="font-medium">{themeConfig.displayName}</h3>
      </div>
      
      <div className="grid grid-cols-5 gap-2">
        {Object.entries(themeConfig.cssVariables)
          .filter(([key]) => key.includes('--primary') || key.includes('--secondary') || key.includes('--accent'))
          .slice(0, 5)
          .map(([key, value]) => (
            <div key={key} className="space-y-1">
              <div
                className="w-full h-8 rounded border"
                style={{ backgroundColor: `hsl(${value})` }}
              />
              <p className="text-xs text-muted-foreground capitalize">
                {key.replace('--', '').replace('-', ' ')}
              </p>
            </div>
          ))}
      </div>
    </div>
  );
}
