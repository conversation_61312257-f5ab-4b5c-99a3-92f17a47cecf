/* Button Component Styles */
/* Component-scoped styles using CSS Modules */

.button {
  /* Base button styles using design tokens */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: var(--easing-out);
  transition-duration: var(--duration-150);
  text-decoration: none;
  white-space: nowrap;
}

.button:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

.button:disabled {
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
  pointer-events: none;
}

/* Size Variants */
.sizeXs {
  height: var(--button-height-sm);
  padding: 0 var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
}

.sizeSm {
  height: var(--button-height-sm);
  padding: 0 var(--spacing-3);
  font-size: var(--font-size-sm);
}

.sizeDefault {
  height: var(--button-height-base);
  padding: 0 var(--spacing-4);
  font-size: var(--font-size-sm);
}

.sizeLg {
  height: var(--button-height-lg);
  padding: 0 var(--spacing-8);
  font-size: var(--font-size-base);
  border-radius: var(--border-radius-lg);
}

.sizeXl {
  height: var(--button-height-xl);
  padding: 0 var(--spacing-10);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-lg);
}

.sizeIcon {
  width: var(--button-height-base);
  height: var(--button-height-base);
  padding: 0;
}

.sizeIconSm {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  padding: 0;
}

.sizeIconLg {
  width: var(--button-height-lg);
  height: var(--button-height-lg);
  padding: 0;
}

/* Style Variants */
.variantDefault {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  box-shadow: var(--shadow-sm);
}

.variantDefault:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.variantDefault:active {
  background-color: hsl(var(--primary) / 0.95);
  transform: translateY(1px);
}

.variantSecondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  box-shadow: var(--shadow-sm);
}

.variantSecondary:hover {
  background-color: hsl(var(--secondary) / 0.8);
}

.variantOutline {
  border-color: hsl(var(--border));
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  box-shadow: var(--shadow-sm);
}

.variantOutline:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.variantGhost {
  background-color: transparent;
  color: hsl(var(--foreground));
}

.variantGhost:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.variantLink {
  background-color: transparent;
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 4px;
}

.variantLink:hover {
  text-decoration: none;
}

.variantDestructive {
  background-color: hsl(var(--destructive));
  color: hsl(var(--destructive-foreground));
  box-shadow: var(--shadow-sm);
}

.variantDestructive:hover {
  background-color: hsl(var(--destructive) / 0.9);
}

.variantSuccess {
  background-color: var(--color-success-500);
  color: white;
  box-shadow: var(--shadow-sm);
}

.variantSuccess:hover {
  background-color: var(--color-success-600);
}

.variantWarning {
  background-color: var(--color-warning-500);
  color: white;
  box-shadow: var(--shadow-sm);
}

.variantWarning:hover {
  background-color: var(--color-warning-600);
}

.variantInfo {
  background-color: var(--color-primary-500);
  color: white;
  box-shadow: var(--shadow-sm);
}

.variantInfo:hover {
  background-color: var(--color-primary-600);
}

.variantGradient {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  color: hsl(var(--primary-foreground));
  box-shadow: var(--shadow-md);
}

.variantGradient:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.7));
  box-shadow: var(--shadow-lg);
}

.variantElevated {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-color: hsl(var(--border));
  box-shadow: var(--shadow-lg);
}

.variantElevated:hover {
  background-color: hsl(var(--accent) / 0.5);
  box-shadow: var(--shadow-xl);
  transform: translateY(-1px);
}

/* State Modifiers */
.loading {
  cursor: not-allowed;
  opacity: var(--opacity-loading);
}

.fullWidth {
  width: 100%;
}

/* Interactive Effects */
.interactive {
  transition: all var(--duration-200) var(--easing-out);
}

.interactive:hover {
  transform: translateY(-1px);
}

.interactive:active {
  transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .button {
    min-height: 44px; /* Touch target size */
  }
  
  .sizeXs,
  .sizeSm {
    min-height: 40px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .button {
    border-width: 2px;
  }
  
  .variantGhost,
  .variantLink {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .button {
    transition: none;
  }
  
  .interactive:hover,
  .interactive:active {
    transform: none;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .variantDefault {
    box-shadow: var(--shadow-md);
  }
  
  .variantElevated {
    box-shadow: var(--shadow-xl);
  }
}
