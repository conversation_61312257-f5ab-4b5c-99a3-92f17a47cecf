# Database Schema & Migration Guide

This document covers the database architecture, schema design, and migration patterns used in the AI Chatbot project.

## Database Architecture

The project uses **PostgreSQL** with **Drizzle ORM** for type-safe database operations and migrations.

### Technology Stack
- **Database**: PostgreSQL (Neon, Supabase, or self-hosted)
- **ORM**: Drizzle ORM for type-safe queries
- **Migrations**: Drizzle Kit for schema management
- **Connection**: postgres.js for connection pooling

## Schema Overview

### Core Tables

```sql
-- Users table for authentication
CREATE TABLE "User" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(64) NOT NULL,
  password VARCHAR(64)
);

-- Chats table for conversation metadata
CREATE TABLE "Chat" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "createdAt" TIMESTAMP NOT NULL,
  title TEXT NOT NULL,
  "userId" UUID NOT NULL REFERENCES "User"(id),
  visibility VARCHAR CHECK (visibility IN ('public', 'private')) NOT NULL DEFAULT 'private'
);

-- Messages table (current version with parts)
CREATE TABLE "Message_v2" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "chatId" UUID NOT NULL REFERENCES "Chat"(id),
  role VARCHAR NOT NULL,
  parts JSON NOT NULL,
  attachments JSON NOT NULL,
  "createdAt" TIMESTAMP NOT NULL
);

-- Documents table for artifacts
CREATE TABLE "Document" (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  "createdAt" TIMESTAMP NOT NULL,
  title TEXT NOT NULL,
  content TEXT,
  kind VARCHAR CHECK (kind IN ('text', 'code', 'image', 'sheet')) NOT NULL DEFAULT 'text',
  "userId" UUID NOT NULL REFERENCES "User"(id),
  PRIMARY KEY (id, "createdAt")
);

-- Votes table for message feedback
CREATE TABLE "Vote_v2" (
  "chatId" UUID NOT NULL REFERENCES "Chat"(id),
  "messageId" UUID NOT NULL REFERENCES "Message_v2"(id),
  "isUpvoted" BOOLEAN NOT NULL,
  PRIMARY KEY ("chatId", "messageId")
);

-- Suggestions table for AI-powered improvements
CREATE TABLE "Suggestion" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "documentId" UUID NOT NULL,
  "documentCreatedAt" TIMESTAMP NOT NULL,
  "originalText" TEXT NOT NULL,
  "suggestedText" TEXT NOT NULL,
  description TEXT,
  "isResolved" BOOLEAN NOT NULL DEFAULT false,
  "userId" UUID NOT NULL REFERENCES "User"(id),
  "createdAt" TIMESTAMP NOT NULL,
  FOREIGN KEY ("documentId", "documentCreatedAt") REFERENCES "Document"(id, "createdAt")
);

-- Streams table for resumable streams
CREATE TABLE "Stream" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "chatId" UUID NOT NULL REFERENCES "Chat"(id),
  "createdAt" TIMESTAMP NOT NULL
);
```

## Drizzle Schema Definition

### User Schema

```typescript
// lib/db/schema.ts
import { pgTable, varchar, uuid } from 'drizzle-orm/pg-core';

export const user = pgTable('User', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  email: varchar('email', { length: 64 }).notNull(),
  password: varchar('password', { length: 64 })
});

export type User = InferSelectModel<typeof user>;
```

### Chat Schema

```typescript
export const chat = pgTable('Chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull(),
  title: text('title').notNull(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id),
  visibility: varchar('visibility', { enum: ['public', 'private'] })
    .notNull()
    .default('private')
});

export type Chat = InferSelectModel<typeof chat>;
```

### Message Schema (Current)

```typescript
export const message = pgTable('Message_v2', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  role: varchar('role').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  createdAt: timestamp('createdAt').notNull()
});

export type DBMessage = InferSelectModel<typeof message>;
```

### Document Schema

```typescript
export const document = pgTable(
  'Document',
  {
    id: uuid('id').notNull().defaultRandom(),
    createdAt: timestamp('createdAt').notNull(),
    title: text('title').notNull(),
    content: text('content'),
    kind: varchar('text', { enum: ['text', 'code', 'image', 'sheet'] })
      .notNull()
      .default('text'),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id)
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] })
    };
  }
);

export type Document = InferSelectModel<typeof document>;
```

## Message Parts Structure

The current message system uses a parts-based approach for flexibility:

### Message Parts Types

```typescript
type MessagePart = 
  | { type: 'text'; text: string }
  | { type: 'tool-invocation'; toolInvocation: ToolInvocation }
  | { type: 'tool-result'; toolResult: any }
  | { type: 'reasoning'; reasoning: string };

interface ToolInvocation {
  toolName: string;
  toolCallId: string;
  state: 'call' | 'result';
  args?: Record<string, any>;
  result?: any;
}
```

### Example Message Structure

```json
{
  "id": "msg-123",
  "chatId": "chat-456",
  "role": "assistant",
  "parts": [
    {
      "type": "text",
      "text": "I'll help you create a document."
    },
    {
      "type": "tool-invocation",
      "toolInvocation": {
        "toolName": "createDocument",
        "toolCallId": "call-789",
        "state": "call",
        "args": {
          "title": "My Document",
          "kind": "text"
        }
      }
    }
  ],
  "attachments": [],
  "createdAt": "2024-01-01T00:00:00Z"
}
```

## Migration Patterns

### Migration Scripts Location

```
lib/db/
├── migrations/          # Generated migration files
├── helpers/            # Migration helper scripts
│   └── 01-core-to-parts.ts
├── schema.ts           # Current schema definition
├── queries.ts          # Database queries
└── migrate.ts          # Migration runner
```

### Running Migrations

```bash
# Generate migration from schema changes
pnpm db:generate

# Run migrations
pnpm db:migrate

# Push schema directly (development only)
pnpm db:push

# Open database studio
pnpm db:studio
```

### Migration Example: Message Parts

The project migrated from content-based messages to parts-based messages:

#### Step 1: Create New Tables

```typescript
// Keep old tables as deprecated
export const messageDeprecated = pgTable('Message', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId').notNull().references(() => chat.id),
  role: varchar('role').notNull(),
  content: json('content').notNull(),
  createdAt: timestamp('createdAt').notNull()
});

// Create new tables with updated schema
export const message = pgTable('Message_v2', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId').notNull().references(() => chat.id),
  role: varchar('role').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  createdAt: timestamp('createdAt').notNull()
});
```

#### Step 2: Migration Script

```typescript
// lib/db/helpers/01-core-to-parts.ts
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { messageDeprecated, message, voteDeprecated, vote } from '../schema';

const runMigration = async () => {
  const connection = postgres(process.env.POSTGRES_URL!);
  const db = drizzle(connection);

  // Migrate messages
  const oldMessages = await db.select().from(messageDeprecated);
  
  for (const oldMessage of oldMessages) {
    const newMessage = {
      id: oldMessage.id,
      chatId: oldMessage.chatId,
      role: oldMessage.role,
      parts: transformContentToParts(oldMessage.content),
      attachments: [],
      createdAt: oldMessage.createdAt
    };
    
    await db.insert(message).values(newMessage);
  }

  // Migrate votes
  const oldVotes = await db.select().from(voteDeprecated);
  
  for (const oldVote of oldVotes) {
    await db.insert(vote).values({
      chatId: oldVote.chatId,
      messageId: oldVote.messageId,
      isUpvoted: oldVote.isUpvoted
    });
  }
};

function transformContentToParts(content: any): MessagePart[] {
  // Transform old content format to new parts format
  if (typeof content === 'string') {
    return [{ type: 'text', text: content }];
  }
  
  // Handle tool calls and other content types
  return content.map(transformContentItem);
}
```

## Database Queries

### Common Query Patterns

```typescript
// lib/db/queries.ts
import { db } from './index';
import { chat, message, document, user } from './schema';
import { eq, desc, and } from 'drizzle-orm';

// Get chat with messages
export async function getChatById({ id }: { id: string }) {
  const [chatResult] = await db
    .select()
    .from(chat)
    .where(eq(chat.id, id))
    .limit(1);
    
  return chatResult;
}

// Get messages for a chat
export async function getMessagesByChatId({ id }: { id: string }) {
  return await db
    .select()
    .from(message)
    .where(eq(message.chatId, id))
    .orderBy(message.createdAt);
}

// Save multiple messages
export async function saveMessages({ 
  messages 
}: { 
  messages: Array<typeof message.$inferInsert> 
}) {
  return await db.insert(message).values(messages);
}

// Get user's chats with pagination
export async function getChatsByUserId({
  id,
  limit = 10,
  startingAfter,
  endingBefore
}: {
  id: string;
  limit?: number;
  startingAfter?: string;
  endingBefore?: string;
}) {
  let query = db
    .select()
    .from(chat)
    .where(eq(chat.userId, id))
    .orderBy(desc(chat.createdAt))
    .limit(limit);

  if (startingAfter) {
    query = query.where(
      and(
        eq(chat.userId, id),
        lt(chat.createdAt, new Date(startingAfter))
      )
    );
  }

  if (endingBefore) {
    query = query.where(
      and(
        eq(chat.userId, id),
        gt(chat.createdAt, new Date(endingBefore))
      )
    );
  }

  return await query;
}
```

### Transaction Examples

```typescript
// Save chat and initial message in transaction
export async function createChatWithMessage({
  chatData,
  messageData
}: {
  chatData: typeof chat.$inferInsert;
  messageData: typeof message.$inferInsert;
}) {
  return await db.transaction(async (tx) => {
    await tx.insert(chat).values(chatData);
    await tx.insert(message).values(messageData);
  });
}
```

## Best Practices

### Schema Design
1. **Use UUIDs** for primary keys to avoid conflicts
2. **Composite keys** for versioned entities (documents)
3. **Proper foreign keys** for data integrity
4. **JSON columns** for flexible, structured data
5. **Enums** for constrained values

### Migration Strategy
1. **Backward compatibility** during transitions
2. **Gradual migration** with helper scripts
3. **Data validation** before and after migration
4. **Rollback plans** for failed migrations
5. **Testing** on staging environment first

### Query Optimization
1. **Proper indexing** on frequently queried columns
2. **Connection pooling** for performance
3. **Pagination** for large result sets
4. **Transactions** for data consistency
5. **Type safety** with Drizzle ORM

This database design provides a solid foundation for the AI Chatbot's data persistence needs while maintaining flexibility for future enhancements.
