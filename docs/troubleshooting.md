# Troubleshooting Guide

This document provides solutions to common issues encountered when developing, deploying, and maintaining the AI Chatbot project.

## Common Issues

### 1. Environment & Configuration Issues

#### Issue: Environment Variables Not Loading

**Symptoms:**
- Application fails to start
- Database connection errors
- API authentication failures

**Solutions:**

```bash
# Check environment file location
ls -la .env.local .env

# Verify environment variables are set
echo $AUTH_SECRET
echo $XAI_API_KEY
echo $POSTGRES_URL

# For Vercel deployment, pull environment variables
vercel env pull
```

**Prevention:**
- Use `.env.example` as a template
- Validate required environment variables on startup
- Use environment variable validation schema

```typescript
// lib/env.ts
import { z } from 'zod';

const envSchema = z.object({
  AUTH_SECRET: z.string().min(32),
  XAI_API_KEY: z.string().min(1),
  POSTGRES_URL: z.string().url(),
  BLOB_READ_WRITE_TOKEN: z.string().min(1)
});

export const env = envSchema.parse(process.env);
```

#### Issue: Database Connection Failures

**Symptoms:**
- "Connection refused" errors
- Timeout errors during database operations
- Migration failures

**Solutions:**

```typescript
// Check connection string format
const validFormats = [
  'postgresql://user:password@host:port/database',
  'postgres://user:password@host:port/database'
];

// Test connection
import postgres from 'postgres';

const testConnection = async () => {
  try {
    const sql = postgres(process.env.POSTGRES_URL!);
    await sql`SELECT 1`;
    console.log('Database connection successful');
    await sql.end();
  } catch (error) {
    console.error('Database connection failed:', error);
  }
};
```

**Common Fixes:**
- Verify database URL format and credentials
- Check network connectivity and firewall settings
- Ensure database server is running
- Verify SSL requirements for production databases

### 2. Authentication Issues

#### Issue: Session Not Persisting

**Symptoms:**
- Users get logged out frequently
- Authentication state not maintained across page refreshes
- Redirect loops to login page

**Solutions:**

```typescript
// Check AUTH_SECRET configuration
// app/(auth)/auth.ts
export const authConfig = {
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string;
        token.type = user.type;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.type = token.type;
      }
      return session;
    }
  }
};
```

**Debugging Steps:**
1. Check browser cookies for session tokens
2. Verify AUTH_SECRET is consistent across deployments
3. Check middleware configuration
4. Validate JWT token structure

#### Issue: Guest Login Not Working

**Symptoms:**
- Guest login button doesn't respond
- Error creating guest users
- Guest sessions not maintained

**Solutions:**

```typescript
// Verify guest user creation
// lib/db/queries.ts
export async function createGuestUser() {
  try {
    const guestUser = {
      id: generateUUID(),
      email: `guest-${Date.now()}@example.com`,
      password: null
    };
    
    await db.insert(user).values(guestUser);
    return [guestUser];
  } catch (error) {
    console.error('Failed to create guest user:', error);
    throw error;
  }
}
```

### 3. AI Model & API Issues

#### Issue: AI Responses Not Streaming

**Symptoms:**
- Long delays before responses appear
- Responses appear all at once instead of streaming
- Timeout errors during generation

**Solutions:**

```typescript
// Check streaming configuration
// app/(chat)/api/chat/route.ts
const stream = createUIMessageStream({
  execute: ({ writer: dataStream }) => {
    const result = streamText({
      model: myProvider.languageModel(selectedChatModel),
      system: systemPrompt({ selectedChatModel, requestHints }),
      messages: convertToModelMessages(uiMessages),
      experimental_transform: smoothStream({ chunking: 'word' }),
      // Ensure streaming is enabled
      stream: true
    });
    
    return result;
  }
});
```

**Debugging:**
- Check network tab for streaming responses
- Verify model provider configuration
- Test with different models
- Check for rate limiting

#### Issue: Tool Calls Not Working

**Symptoms:**
- Tools not being invoked by AI
- Tool results not appearing in chat
- Error messages about unknown tools

**Solutions:**

```typescript
// Verify tool registration
// app/(chat)/api/chat/route.ts
const result = streamText({
  model: myProvider.languageModel(selectedChatModel),
  tools: {
    getWeather,
    createDocument: createDocument({ session, dataStream }),
    updateDocument: updateDocument({ session, dataStream }),
    requestSuggestions: requestSuggestions({ session, dataStream })
  },
  experimental_activeTools: [
    'getWeather',
    'createDocument', 
    'updateDocument',
    'requestSuggestions'
  ]
});
```

**Common Issues:**
- Tool not included in `experimental_activeTools`
- Tool function throwing errors
- Incorrect tool parameter schemas
- Model not supporting tool calling

### 4. Artifact System Issues

#### Issue: Artifacts Not Displaying

**Symptoms:**
- Artifact button doesn't appear
- Artifact panel remains empty
- Document creation fails silently

**Solutions:**

```typescript
// Check artifact registration
// components/artifact.tsx
export const artifactDefinitions = [
  textArtifact,
  codeArtifact,
  imageArtifact,
  sheetArtifact
  // Ensure your custom artifact is included
];

// Verify server handler registration
// lib/artifacts/server.ts
export const documentHandlersByArtifactKind = [
  textDocumentHandler,
  codeDocumentHandler,
  imageDocumentHandler,
  sheetDocumentHandler
  // Ensure handler is registered
];
```

**Debugging Steps:**
1. Check browser console for JavaScript errors
2. Verify artifact kind in database schema
3. Test document creation API endpoint directly
4. Check artifact streaming data

#### Issue: Code Execution Not Working

**Symptoms:**
- Python code doesn't execute
- Pyodide loading errors
- Code output not displaying

**Solutions:**

```typescript
// Check Pyodide loading
// app/(chat)/layout.tsx
<Script
  src="https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js"
  strategy="beforeInteractive"
/>

// Verify code execution
// artifacts/code/client.tsx
const runCode = async (code: string) => {
  try {
    if (!window.pyodide) {
      window.pyodide = await loadPyodide();
    }
    
    const result = await window.pyodide.runPython(code);
    return result;
  } catch (error) {
    console.error('Code execution error:', error);
    throw error;
  }
};
```

### 5. Database & Migration Issues

#### Issue: Migration Failures

**Symptoms:**
- Migration scripts fail to run
- Database schema out of sync
- Foreign key constraint errors

**Solutions:**

```bash
# Check migration status
pnpm db:check

# Generate new migration
pnpm db:generate

# Run migrations manually
pnpm db:migrate

# Reset database (development only)
pnpm db:push --force
```

**Common Fixes:**
- Ensure database is accessible
- Check for conflicting schema changes
- Verify foreign key relationships
- Run migrations in correct order

#### Issue: Message Parts Migration

**Symptoms:**
- Old messages not displaying correctly
- Tool calls not rendering properly
- Message history appears broken

**Solutions:**

```bash
# Run message parts migration
pnpm exec tsx lib/db/helpers/01-core-to-parts.ts

# Verify migration completed
# Check both old and new message tables
```

```typescript
// Update message rendering to handle both formats
// components/message.tsx
const renderMessageContent = (message: ChatMessage) => {
  // Handle new parts format
  if (message.parts && message.parts.length > 0) {
    return message.parts.map(renderMessagePart);
  }
  
  // Fallback to old content format
  if (message.content) {
    return <Markdown>{message.content}</Markdown>;
  }
  
  return null;
};
```

### 6. Performance Issues

#### Issue: Slow Page Loading

**Symptoms:**
- Long initial page load times
- Slow navigation between pages
- High memory usage

**Solutions:**

```typescript
// Optimize bundle size
// next.config.ts
const nextConfig = {
  experimental: {
    optimizePackageImports: ['@radix-ui/react-icons']
  },
  webpack: (config) => {
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    };
    return config;
  }
};
```

**Performance Optimizations:**
- Enable code splitting
- Optimize images and fonts
- Use React.memo for expensive components
- Implement proper caching strategies

#### Issue: Memory Leaks

**Symptoms:**
- Increasing memory usage over time
- Browser becomes unresponsive
- Frequent garbage collection

**Solutions:**

```typescript
// Proper cleanup in useEffect
useEffect(() => {
  const subscription = someObservable.subscribe();
  
  return () => {
    subscription.unsubscribe();
  };
}, []);

// Cleanup SWR cache when needed
import { mutate } from 'swr';

const clearCache = () => {
  mutate(() => true, undefined, { revalidate: false });
};
```

### 7. Deployment Issues

#### Issue: Build Failures

**Symptoms:**
- TypeScript compilation errors
- Missing dependencies
- Environment variable errors during build

**Solutions:**

```bash
# Check for TypeScript errors
pnpm tsc --noEmit

# Verify all dependencies are installed
pnpm install --frozen-lockfile

# Check build locally
pnpm build

# Debug build issues
NEXT_DEBUG=1 pnpm build
```

#### Issue: Runtime Errors in Production

**Symptoms:**
- Application works locally but fails in production
- 500 internal server errors
- Missing environment variables

**Solutions:**

```typescript
// Add error boundaries
// components/error-boundary.tsx
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }

    return this.props.children;
  }
}
```

## Debugging Tools

### 1. Development Tools

```bash
# Database inspection
pnpm db:studio

# Check environment variables
pnpm exec tsx -e "console.log(process.env)"

# Test API endpoints
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"id":"test","message":{"role":"user","parts":[{"type":"text","text":"Hello"}]}}'
```

### 2. Browser Debugging

```javascript
// Debug SWR cache
window.__SWR_DEVTOOLS_USE__ = []

// Debug React components
window.React = require('react')

// Check authentication state
console.log(document.cookie)
```

### 3. Server Debugging

```typescript
// Add detailed logging
// lib/logger.ts
export const logger = {
  info: (message: string, data?: any) => {
    console.log(`[INFO] ${message}`, data);
  },
  error: (message: string, error?: any) => {
    console.error(`[ERROR] ${message}`, error);
  },
  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data);
    }
  }
};
```

## Getting Help

### 1. Check Logs
- Browser console for client-side errors
- Server logs for API errors
- Database logs for query issues
- Network tab for request/response debugging

### 2. Community Resources
- [GitHub Issues](https://github.com/vercel/ai-chatbot/issues)
- [Chat SDK Documentation](https://chat-sdk.dev/docs)
- [AI SDK Documentation](https://sdk.vercel.ai/docs)
- [Next.js Documentation](https://nextjs.org/docs)

### 3. Create Minimal Reproduction
When reporting issues:
1. Isolate the problem to minimal code
2. Include environment details
3. Provide step-by-step reproduction
4. Include error messages and logs
5. Specify expected vs actual behavior

This troubleshooting guide should help resolve most common issues encountered during development and deployment of the AI Chatbot project.
