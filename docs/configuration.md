# Configuration Guide

This document covers all configuration options for the AI Chatbot project, including environment setup, model providers, and customization options.

## Environment Variables

### Required Variables

```bash
# Authentication
AUTH_SECRET=your-32-character-secret-key
# Generate with: https://generate-secret.vercel.app/32
# Or: openssl rand -base64 32

# AI Model Provider
XAI_API_KEY=your-xai-api-key
# Get from: https://console.x.ai/

# Database
POSTGRES_URL=postgresql://user:password@host:port/database
# Neon: https://vercel.com/marketplace/neon
# Supabase: https://supabase.com/

# File Storage
BLOB_READ_WRITE_TOKEN=your-vercel-blob-token
# Create at: https://vercel.com/docs/storage/vercel-blob
```

### Optional Variables

```bash
# Caching & Resumable Streams
REDIS_URL=redis://user:password@host:port
# Or use KV_URL for Vercel KV
KV_URL=your-vercel-kv-url

# Development
NODE_ENV=development|production
PLAYWRIGHT=true  # For testing environment
```

## Model Provider Configuration

### Default Configuration (xAI)

```typescript
// lib/ai/providers.ts
import { customProvider } from 'ai';
import { xai } from '@ai-sdk/xai';

export const myProvider = customProvider({
  languageModels: {
    'chat-model': xai('grok-2-vision-1212'),
    'chat-model-reasoning': wrapLanguageModel({
      model: xai('grok-3-mini-beta'),
      middleware: extractReasoningMiddleware({ tagName: 'think' })
    }),
    'title-model': xai('grok-2-1212'),
    'artifact-model': xai('grok-2-1212')
  },
  imageModels: {
    'small-model': xai.imageModel('grok-2-image')
  }
});
```

### Alternative Providers

#### OpenAI Configuration

```typescript
import { openai } from '@ai-sdk/openai';

export const myProvider = customProvider({
  languageModels: {
    'chat-model': openai('gpt-4-turbo'),
    'chat-model-reasoning': openai('gpt-4o'),
    'title-model': openai('gpt-3.5-turbo'),
    'artifact-model': openai('gpt-4-turbo')
  },
  imageModels: {
    'small-model': openai.image('dall-e-3')
  }
});
```

#### Anthropic Configuration

```typescript
import { anthropic } from '@ai-sdk/anthropic';

export const myProvider = customProvider({
  languageModels: {
    'chat-model': anthropic('claude-3-5-sonnet-20241022'),
    'chat-model-reasoning': anthropic('claude-3-5-sonnet-20241022'),
    'title-model': anthropic('claude-3-haiku-20240307'),
    'artifact-model': anthropic('claude-3-5-sonnet-20241022')
  }
  // Note: Anthropic doesn't provide image generation models
});
```

#### Mixed Provider Configuration

```typescript
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { xai } from '@ai-sdk/xai';

export const myProvider = customProvider({
  languageModels: {
    'chat-model': anthropic('claude-3-5-sonnet-20241022'),
    'chat-model-reasoning': openai('gpt-4o'),
    'title-model': openai('gpt-3.5-turbo'),
    'artifact-model': anthropic('claude-3-5-sonnet-20241022')
  },
  imageModels: {
    'small-model': xai.imageModel('grok-2-image')
  }
});
```

## Database Configuration

### Drizzle Configuration

```typescript
// drizzle.config.ts
import { config } from 'dotenv';
import { defineConfig } from 'drizzle-kit';

config({ path: '.env.local' });

export default defineConfig({
  schema: './lib/db/schema.ts',
  out: './lib/db/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.POSTGRES_URL!
  }
});
```

### Connection Pool Settings

```typescript
// lib/db/index.ts
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

const connection = postgres(process.env.POSTGRES_URL!, {
  max: 10,          // Maximum connections
  idle_timeout: 20, // Idle timeout in seconds
  connect_timeout: 10 // Connection timeout in seconds
});

export const db = drizzle(connection);
```

## Authentication Configuration

### Auth.js Configuration

```typescript
// app/(auth)/auth.config.ts
import type { NextAuthConfig } from 'next-auth';

export const authConfig = {
  pages: {
    signIn: '/login',
    newUser: '/'
  },
  providers: [
    // Providers added in auth.ts
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string;
        token.type = user.type;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.type = token.type;
      }
      return session;
    }
  }
} satisfies NextAuthConfig;
```

### Session Configuration

```typescript
// app/(auth)/auth.ts
export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut
} = NextAuth({
  ...authConfig,
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60 // 30 days
  },
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        // Custom authentication logic
      }
    })
  ]
});
```

## UI Configuration

### Theme Configuration

```css
/* app/globals.css */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
  }
  
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    /* ... dark theme variables */
  }
}
```

### Font Configuration

```typescript
// app/layout.tsx
import { Inter, Roboto_Mono } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter'
});

const roboto_mono = Roboto_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto-mono'
});

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${roboto_mono.variable}`}>
      <body className="antialiased">{children}</body>
    </html>
  );
}
```

### Tailwind Configuration

```typescript
// tailwind.config.ts
import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}'
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-inter)'],
        mono: ['var(--font-roboto-mono)']
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        }
        // ... other color definitions
      }
    }
  },
  plugins: [require('tailwindcss-animate')]
};

export default config;
```

## Next.js Configuration

### Basic Configuration

```typescript
// next.config.ts
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    ppr: true // Partial Prerendering
  },
  images: {
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh'
      }
    ]
  }
};

export default nextConfig;
```

### Middleware Configuration

```typescript
// middleware.ts
import { auth } from '@/app/(auth)/auth';
import { NextResponse } from 'next/server';

export default auth((req) => {
  const { pathname } = req.nextUrl;
  
  // Protect chat routes
  if (pathname.startsWith('/chat') && !req.auth) {
    return NextResponse.redirect(new URL('/login', req.url));
  }
  
  return NextResponse.next();
});

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)']
};
```

## Development Configuration

### Package.json Scripts

```json
{
  "scripts": {
    "dev": "next dev --turbo",
    "build": "tsx lib/db/migrate && next build",
    "start": "next start",
    "lint": "next lint && biome lint --write --unsafe",
    "lint:fix": "next lint --fix && biome lint --write --unsafe",
    "format": "biome format --write",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "npx tsx lib/db/migrate.ts",
    "db:studio": "drizzle-kit studio",
    "db:push": "drizzle-kit push",
    "test": "export PLAYWRIGHT=True && pnpm exec playwright test"
  }
}
```

### Biome Configuration

```json
// biome.jsonc
{
  "$schema": "https://biomejs.dev/schemas/1.9.4/schema.json",
  "vcs": {
    "enabled": false,
    "clientKind": "git",
    "useIgnoreFile": false
  },
  "files": {
    "ignoreUnknown": false,
    "ignore": []
  },
  "formatter": {
    "enabled": true,
    "indentStyle": "space"
  },
  "organizeImports": {
    "enabled": true
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": false
    }
  },
  "javascript": {
    "formatter": {
      "quoteStyle": "single"
    }
  }
}
```

## Production Configuration

### Environment Setup

```bash
# Production environment variables
NODE_ENV=production
AUTH_SECRET=production-secret-key
XAI_API_KEY=production-api-key
POSTGRES_URL=production-database-url
BLOB_READ_WRITE_TOKEN=production-blob-token
REDIS_URL=production-redis-url
```

### Build Optimization

```typescript
// next.config.ts (production)
const nextConfig: NextConfig = {
  experimental: {
    ppr: true
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  images: {
    formats: ['image/webp', 'image/avif'],
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh'
      }
    ]
  }
};
```

This configuration guide provides all the necessary settings to customize and deploy the AI Chatbot project successfully.
