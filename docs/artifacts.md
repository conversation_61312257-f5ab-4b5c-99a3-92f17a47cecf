# Artifacts System

The Artifacts system is a core feature that provides workspace-like interfaces for complex user interactions, similar to ChatGPT's Can<PERSON> and Claude's Artifacts.

## Overview

Artifacts allow users to work with different types of content in dedicated workspaces alongside the chat interface:

- **Text Artifact**: Rich text editing with AI-powered suggestions
- **Code Artifact**: Python code execution with <PERSON><PERSON><PERSON><PERSON>
- **Image Artifact**: AI-powered image generation and editing
- **Sheet Artifact**: Interactive spreadsheet functionality

## Architecture

### File Structure

Each artifact type follows a consistent structure:

```
artifacts/
  {type}/
    client.tsx    # UI components and client-side logic
    server.ts     # Server-side processing and streaming
```

### Registration Points

To add a new artifact, it must be registered in multiple locations:

1. **Server Handler**: `lib/artifacts/server.ts`
2. **Database Schema**: `lib/db/schema.ts`
3. **Client Definitions**: `components/artifact.tsx`

## Creating Custom Artifacts

### Step 1: Create Artifact Files

Create a new folder in the `artifacts` directory:

```
artifacts/
  custom/
    client.tsx
    server.ts
```

### Step 2: Implement Client-Side Logic

```typescript
// artifacts/custom/client.tsx
import { Artifact } from '@/components/create-artifact';
import { ExampleComponent } from '@/components/example-component';
import { toast } from 'sonner';

interface CustomArtifactMetadata {
  info: string;
  lastUpdated: Date;
}

export const customArtifact = new Artifact<'custom', CustomArtifactMetadata>({
  kind: 'custom',
  description: 'A custom artifact for demonstrating functionality.',
  
  // Initialize artifact with default state
  initialize: async ({ documentId, setMetadata }) => {
    setMetadata({
      info: `Document ${documentId} initialized.`,
      lastUpdated: new Date()
    });
  },
  
  // Handle streaming updates from server
  onStreamPart: ({ streamPart, setMetadata, setArtifact }) => {
    if (streamPart.type === 'info-update') {
      setMetadata((metadata) => ({
        ...metadata,
        info: streamPart.content as string,
        lastUpdated: new Date()
      }));
    }
    
    if (streamPart.type === 'content-update') {
      setArtifact((draftArtifact) => ({
        ...draftArtifact,
        content: draftArtifact.content + (streamPart.content as string),
        status: 'streaming'
      }));
    }
  },
  
  // Render artifact content
  content: ({
    mode,
    status,
    content,
    isCurrentVersion,
    currentVersionIndex,
    onSaveContent,
    getDocumentContentById,
    isLoading,
    metadata
  }) => {
    if (isLoading) {
      return <div>Loading custom artifact...</div>;
    }

    // Diff view for comparing versions
    if (mode === 'diff') {
      const oldContent = getDocumentContentById(currentVersionIndex - 1);
      const newContent = getDocumentContentById(currentVersionIndex);
      return (
        <div>
          <h3>Diff View</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4>Previous</h4>
              <pre>{oldContent}</pre>
            </div>
            <div>
              <h4>Current</h4>
              <pre>{newContent}</pre>
            </div>
          </div>
        </div>
      );
    }

    // Main content view
    return (
      <div className="custom-artifact">
        <ExampleComponent
          content={content}
          metadata={metadata}
          onSaveContent={onSaveContent}
          isCurrentVersion={isCurrentVersion}
        />
        <button
          onClick={() => {
            navigator.clipboard.writeText(content);
            toast.success('Content copied to clipboard!');
          }}
        >
          Copy Content
        </button>
      </div>
    );
  },
  
  // Toolbar actions
  actions: [
    {
      icon: <span>⟳</span>,
      description: 'Refresh artifact info',
      onClick: ({ appendMessage }) => {
        appendMessage({
          role: 'user',
          content: 'Please refresh the info for my custom artifact.'
        });
      }
    }
  ],
  
  // Additional toolbar items
  toolbar: [
    {
      icon: <span>✎</span>,
      description: 'Edit custom artifact',
      onClick: ({ appendMessage }) => {
        appendMessage({
          role: 'user',
          content: 'Edit the custom artifact content.'
        });
      }
    }
  ]
});
```

### Step 3: Implement Server-Side Logic

```typescript
// artifacts/custom/server.ts
import { smoothStream, streamText } from 'ai';
import { myProvider } from '@/lib/ai/providers';
import { createDocumentHandler } from '@/lib/artifacts/server';
import { updateDocumentPrompt } from '@/lib/ai/prompts';

export const customDocumentHandler = createDocumentHandler<'custom'>({
  kind: 'custom',
  
  // Handle document creation
  onCreateDocument: async ({ title, dataStream }) => {
    let draftContent = '';
    
    const { fullStream } = streamText({
      model: myProvider.languageModel('artifact-model'),
      system: 'Generate creative content based on the title. Use markdown formatting.',
      experimental_transform: smoothStream({ chunking: 'word' }),
      prompt: title
    });

    // Stream content to client
    for await (const delta of fullStream) {
      if (delta.type === 'text-delta') {
        draftContent += delta.textDelta;
        
        // Send update to client
        dataStream.writeData({
          type: 'content-update',
          content: delta.textDelta
        });
      }
    }

    return draftContent;
  },
  
  // Handle document updates
  onUpdateDocument: async ({ document, description, dataStream }) => {
    let draftContent = '';
    
    const { fullStream } = streamText({
      model: myProvider.languageModel('artifact-model'),
      system: updateDocumentPrompt(document.content, 'custom'),
      experimental_transform: smoothStream({ chunking: 'word' }),
      prompt: description,
      experimental_providerMetadata: {
        openai: {
          prediction: {
            type: 'content',
            content: document.content
          }
        }
      }
    });

    for await (const delta of fullStream) {
      if (delta.type === 'text-delta') {
        draftContent += delta.textDelta;
        
        dataStream.writeData({
          type: 'content-update',
          content: delta.textDelta
        });
      }
    }

    return draftContent;
  }
});
```

### Step 4: Register the Artifact

#### Update Server Handlers

```typescript
// lib/artifacts/server.ts
import { customDocumentHandler } from '@/artifacts/custom/server';

export const documentHandlersByArtifactKind: Array<DocumentHandler> = [
  textDocumentHandler,
  codeDocumentHandler,
  imageDocumentHandler,
  sheetDocumentHandler,
  customDocumentHandler // Add your handler
];

export const artifactKinds = [
  'text',
  'code', 
  'image',
  'sheet',
  'custom' // Add your kind
] as const;
```

#### Update Database Schema

```typescript
// lib/db/schema.ts
export const document = pgTable(
  'Document',
  {
    id: uuid('id').notNull().defaultRandom(),
    createdAt: timestamp('createdAt').notNull(),
    title: text('title').notNull(),
    content: text('content'),
    kind: varchar('text', { 
      enum: ['text', 'code', 'image', 'sheet', 'custom'] // Add your kind
    })
      .notNull()
      .default('text'),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id)
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] })
    };
  }
);
```

#### Update Client Definitions

```typescript
// components/artifact.tsx
import { customArtifact } from '@/artifacts/custom/client';

export const artifactDefinitions = [
  textArtifact,
  codeArtifact,
  imageArtifact,
  sheetArtifact,
  customArtifact // Add your artifact
];
```

## Built-in Artifacts

### Text Artifact

**Purpose**: Rich text editing with AI-powered suggestions

**Features**:
- Markdown rendering and editing
- AI-powered content suggestions
- Version history and diff view
- Copy and export functionality

**Usage**:
```typescript
// Triggered by AI when creating text content
createDocument({
  title: 'Essay Draft',
  kind: 'text'
});
```

### Code Artifact

**Purpose**: Python code execution with live output

**Features**:
- Syntax highlighting with CodeMirror
- Live code execution using Pyodide
- Error handling and output display
- Code sharing and export

**Usage**:
```typescript
// Triggered when AI generates code
createDocument({
  title: 'Data Analysis Script',
  kind: 'code'
});
```

### Image Artifact

**Purpose**: AI-powered image generation and editing

**Features**:
- Image generation using AI models
- Base64 image handling
- Image editing and annotation
- Download and sharing capabilities

**Usage**:
```typescript
// Triggered for image generation requests
createDocument({
  title: 'Sunset Landscape',
  kind: 'image'
});
```

### Sheet Artifact

**Purpose**: Interactive spreadsheet functionality

**Features**:
- Data grid with editing capabilities
- CSV import/export
- Formula support
- Data visualization

**Usage**:
```typescript
// Triggered for data analysis tasks
createDocument({
  title: 'Sales Data Analysis',
  kind: 'sheet'
});
```

## Advanced Features

### Streaming Updates

Artifacts support real-time streaming updates:

```typescript
// Server-side streaming
dataStream.writeData({
  type: 'content-update',
  content: 'New content chunk'
});

// Client-side handling
onStreamPart: ({ streamPart, setArtifact }) => {
  if (streamPart.type === 'content-update') {
    setArtifact(draft => ({
      ...draft,
      content: draft.content + streamPart.content
    }));
  }
}
```

### Version Management

Artifacts maintain version history:

```typescript
// Access previous versions
const oldContent = getDocumentContentById(currentVersionIndex - 1);
const newContent = getDocumentContentById(currentVersionIndex);

// Save new version
onSaveContent(updatedContent);
```

### Metadata Management

Store additional artifact data:

```typescript
interface CustomMetadata {
  lastModified: Date;
  wordCount: number;
  tags: string[];
}

// Update metadata
setMetadata(prev => ({
  ...prev,
  wordCount: content.split(' ').length
}));
```

## Best Practices

1. **Consistent Interface**: Follow the established patterns for client/server structure
2. **Error Handling**: Implement proper error boundaries and fallbacks
3. **Performance**: Use streaming for large content generation
4. **Accessibility**: Ensure artifacts are keyboard navigable and screen reader friendly
5. **Testing**: Create E2E tests for artifact functionality

The Artifacts system provides a powerful foundation for creating rich, interactive experiences within the chat interface.
