# AI Chatbot Design System

A comprehensive, modular design system built for the AI Chatbot application with full customization capabilities.

## Overview

This design system provides:
- **Modular Component Architecture**: Reusable, composable components
- **Advanced Theming System**: Multiple themes with runtime switching
- **Configuration-Driven Customization**: JSON/YAML configs, environment variables, runtime settings
- **Modern UI Enhancements**: Animations, responsive design, accessibility
- **Design Token System**: Consistent values across all components
- **Developer Experience**: TypeScript interfaces, documentation, examples

## Quick Start

### Basic Usage

```tsx
import { Button } from '@/components/ui/button';
import { ThemeSelector } from '@/components/theme-selector';
import { useDesignSystemContext } from '@/components/theme-provider';

function MyComponent() {
  const { currentTheme, setTheme } = useDesignSystemContext();
  
  return (
    <div>
      <Button variant="primary" size="lg">
        Click me
      </Button>
      <ThemeSelector />
    </div>
  );
}
```

### Theme Configuration

```tsx
import { configManager } from '@/lib/design-system/config';

// Set theme programmatically
configManager.setTheme('dark');

// Update configuration
configManager.updateConfig({
  customizations: {
    enableAnimations: false,
    compactMode: true,
  },
});
```

## Theming System

### Available Themes

- **Light**: Clean and bright theme for daytime use
- **Dark**: Easy on the eyes for low-light environments  
- **High Contrast**: Enhanced contrast for better accessibility
- **Ocean Blue**: Professional blue color scheme
- **Forest Green**: Natural green color palette
- **Royal Purple**: Rich purple theme for creativity

### Theme Switching

```tsx
import { ThemeSelector, CompactThemeSelector } from '@/components/theme-selector';

// Full theme selector with previews
<ThemeSelector />

// Compact version for mobile
<CompactThemeSelector />
```

### Custom Themes

Create custom themes by extending the base theme configuration:

```typescript
import type { ThemeConfig } from '@/lib/design-system/types';

const customTheme: ThemeConfig = {
  name: 'custom',
  displayName: 'Custom Theme',
  tokens: defaultTokens,
  cssVariables: {
    '--primary': '350 89% 60%', // Custom primary color
    '--secondary': '240 5% 96%',
    // ... other variables
  },
};
```

## Component Architecture

### Button Component

```tsx
import { Button } from '@/components/ui/button';

// Basic usage
<Button>Click me</Button>

// With variants
<Button variant="secondary" size="lg">
  Large Secondary Button
</Button>

// With icons and loading state
<Button 
  variant="primary" 
  leftIcon={<PlusIcon />}
  loading={isLoading}
  loadingText="Saving..."
>
  Save Changes
</Button>

// Available variants
type ButtonVariant = 
  | 'default' 
  | 'destructive' 
  | 'outline' 
  | 'secondary' 
  | 'ghost' 
  | 'link'
  | 'success'
  | 'warning' 
  | 'info'
  | 'gradient'
  | 'elevated';

type ButtonSize = 'xs' | 'sm' | 'default' | 'lg' | 'xl' | 'icon' | 'icon-sm' | 'icon-lg';
```

### Card Component

```tsx
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

<Card variant="elevated" interactive>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    Card content goes here
  </CardContent>
</Card>

// Available variants
type CardVariant = 'default' | 'outlined' | 'elevated' | 'ghost' | 'filled' | 'gradient';
```

### Input Component

```tsx
import { Input } from '@/components/ui/input';
import { SearchIcon } from '@/components/icons';

<Input 
  variant="outlined"
  size="lg"
  leftIcon={<SearchIcon />}
  placeholder="Search..."
  helperText="Enter your search query"
/>

// With error state
<Input 
  state="error"
  errorText="This field is required"
/>
```

## Configuration System

### Environment Variables

```bash
# .env.local
NEXT_PUBLIC_DEFAULT_THEME=dark
NEXT_PUBLIC_ENABLE_ANIMATIONS=true
NEXT_PUBLIC_COMPACT_MODE=false
NEXT_PUBLIC_HIGH_CONTRAST=false
```

### JSON Configuration

Create `design-system.config.json` in your project root:

```json
{
  "defaultTheme": "light",
  "customizations": {
    "enableAnimations": true,
    "enableTransitions": true,
    "enableShadows": true,
    "compactMode": false,
    "highContrast": false
  },
  "components": {
    "button": {
      "defaultProps": {
        "size": "md",
        "variant": "default"
      }
    }
  }
}
```

### Runtime Settings

```tsx
import { RuntimeSettingsPanel } from '@/components/runtime-settings-panel';

// Add to your app
<RuntimeSettingsPanel />
```

## Design Tokens

### Using Design Tokens

```css
/* In CSS */
.my-component {
  padding: var(--spacing-4);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}
```

```tsx
// In TypeScript
import { getColorValue, getSpacingValue } from '@/lib/design-system/tokens';

const primaryColor = getColorValue('primary', '500');
const mediumSpacing = getSpacingValue('4');
```

### Token Categories

- **Colors**: Primary, secondary, semantic colors with full scales
- **Typography**: Font families, sizes, weights, line heights
- **Spacing**: Consistent spacing scale from 0 to 96
- **Shadows**: Elevation system with multiple shadow levels
- **Border Radius**: Consistent corner radius values
- **Animations**: Duration and easing values

## Utility Classes

### Design System Utilities

```html
<!-- Spacing -->
<div class="ds-p-4 ds-m-2 ds-gap-3">

<!-- Typography -->
<h1 class="ds-text-2xl ds-font-bold ds-leading-tight">

<!-- Colors -->
<div class="ds-bg-primary ds-text-inverse">

<!-- Shadows and Borders -->
<div class="ds-shadow-md ds-rounded-lg ds-border-primary">

<!-- Interactive States -->
<button class="ds-interactive ds-focus-ring">
```

### Component Base Classes

```html
<!-- Button base -->
<button class="ds-button-base">

<!-- Input base -->
<input class="ds-input-base">

<!-- Card base -->
<div class="ds-card-base">
```

## Accessibility Features

### Built-in Accessibility

- **Focus Management**: Visible focus rings and keyboard navigation
- **Color Contrast**: WCAG AA compliant color combinations
- **Reduced Motion**: Respects user's motion preferences
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Touch Targets**: Minimum 44px touch targets on mobile

### High Contrast Mode

```tsx
// Enable high contrast programmatically
configManager.updateConfig({
  customizations: {
    highContrast: true,
  },
});
```

### Accessibility Utilities

```css
.skip-link {
  /* Skip to main content link */
}

.focus-visible-enhanced {
  /* Enhanced focus styles */
}

@media (prefers-reduced-motion: reduce) {
  /* Reduced motion styles */
}

@media (prefers-contrast: high) {
  /* High contrast adjustments */
}
```

## Responsive Design

### Breakpoints

```css
/* Available breakpoints */
--breakpoint-sm: 640px;
--breakpoint-md: 768px;
--breakpoint-lg: 1024px;
--breakpoint-xl: 1280px;
--breakpoint-2xl: 1536px;
```

### Responsive Typography

```html
<h1 class="text-2xl-responsive">Responsive Heading</h1>
<p class="text-base-responsive text-readable">Responsive paragraph</p>
```

### Container System

```html
<div class="ds-container">
  <!-- Responsive container with max-widths -->
</div>
```

## Animation System

### Animation Classes

```html
<!-- Entrance animations -->
<div class="animate-slide-in-left">
<div class="animate-fade-in-scale">
<div class="animate-bounce-in">

<!-- Interactive effects -->
<button class="interactive-scale">
<div class="interactive-lift">
<button class="interactive-glow">

<!-- Loading states -->
<div class="animate-shimmer">
```

### Custom Animations

```css
@keyframes customAnimation {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.custom-enter {
  animation: customAnimation var(--duration-300) var(--easing-out);
}
```

## Best Practices

### Component Development

1. **Use Design Tokens**: Always use design tokens instead of hardcoded values
2. **Follow Naming Conventions**: Use consistent naming for variants and props
3. **Include TypeScript Types**: Provide proper TypeScript interfaces
4. **Add Accessibility**: Include ARIA labels and keyboard support
5. **Test Responsiveness**: Ensure components work on all screen sizes

### Theming

1. **Semantic Colors**: Use semantic color names (primary, secondary) over specific colors
2. **Consistent Scales**: Follow the established color and spacing scales
3. **Test Contrast**: Ensure sufficient color contrast in all themes
4. **Support Dark Mode**: Test all components in both light and dark themes

### Performance

1. **CSS-in-JS Optimization**: Use CSS modules for component-scoped styles
2. **Tree Shaking**: Import only the components you need
3. **Lazy Loading**: Load theme configurations on demand
4. **Minimize Reflows**: Use CSS transforms for animations

## Migration Guide

### From Existing Components

1. **Update Imports**: Change to new component paths
2. **Update Props**: Use new variant and size props
3. **Update Styles**: Replace custom styles with design system utilities
4. **Test Themes**: Verify components work with all themes

### Breaking Changes

- Button `color` prop renamed to `variant`
- Card `elevation` prop replaced with `variant="elevated"`
- Input `error` prop replaced with `state="error"`

## Contributing

### Adding New Components

1. Create component in `components/ui/`
2. Add TypeScript interfaces
3. Include variant system using `cva`
4. Add component styles in `components/styles/`
5. Update documentation
6. Add to Storybook (if available)

### Adding New Themes

1. Create theme configuration in `lib/design-system/themes.ts`
2. Add CSS variables mapping
3. Test with all components
4. Update theme selector
5. Document theme usage

## Support

For questions, issues, or contributions:
- Check the component documentation
- Review TypeScript interfaces
- Test with different themes
- Ensure accessibility compliance
