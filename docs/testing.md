# Testing Guide

This document covers the testing strategy, setup, and best practices for the AI Chatbot project using <PERSON><PERSON> for end-to-end testing.

## Testing Architecture

The project uses **Playwright** for comprehensive end-to-end testing, simulating real user interactions and validating the complete application flow.

### Testing Stack
- **E2E Framework**: Playwright
- **Mock Models**: Custom mock language models for deterministic testing
- **Test Environment**: Isolated test database and environment
- **Browsers**: Chrome, Firefox, WebKit support

## Test Structure

### Directory Organization

```
tests/
├── e2e/                    # End-to-end test files
│   ├── chat.test.ts       # Chat functionality tests
│   ├── auth.test.ts       # Authentication tests
│   └── artifacts.test.ts  # Artifact system tests
├── pages/                 # Page Object Model classes
│   ├── chat.ts           # Chat page interactions
│   ├── auth.ts           # Authentication page interactions
│   └── base.ts           # Base page class
├── prompts/               # Test prompt definitions
│   ├── utils.ts          # Prompt matching utilities
│   └── responses.ts      # Mock response definitions
├── fixtures.ts            # Test fixtures and setup
└── helpers.ts             # Test utility functions
```

## Mock Model System

### Mock Model Configuration

```typescript
// lib/ai/models.test.ts
import { simulateReadableStream } from 'ai';
import { MockLanguageModelV1 } from 'ai/test';
import { getResponseChunksByPrompt } from '@/tests/prompts/utils';

export const chatModel = new MockLanguageModelV1({
  doStream: async ({ prompt }) => ({
    stream: simulateReadableStream({
      chunkDelayInMs: 50,
      initialDelayInMs: 100,
      chunks: getResponseChunksByPrompt(prompt)
    }),
    rawCall: { rawPrompt: null, rawSettings: {} }
  })
});

export const titleModel = new MockLanguageModelV1({
  doGenerate: async ({ prompt }) => ({
    text: generateMockTitle(prompt),
    usage: { completionTokens: 10, promptTokens: 5 }
  })
});

export const artifactModel = new MockLanguageModelV1({
  doStream: async ({ prompt }) => ({
    stream: simulateReadableStream({
      chunks: getArtifactResponseChunks(prompt)
    }),
    rawCall: { rawPrompt: null, rawSettings: {} }
  })
});
```

### Response Mapping

```typescript
// tests/prompts/utils.ts
import { TEST_PROMPTS } from './responses';

export function getResponseChunksByPrompt(prompt: string) {
  const recentMessage = getRecentUserMessage(prompt);
  
  // Weather queries
  if (compareMessages(recentMessage, TEST_PROMPTS.USER_WEATHER)) {
    return [
      ...textToDeltas("I'll get the weather for you."),
      {
        type: 'tool-call',
        toolCallId: 'call-weather-123',
        toolName: 'getWeather',
        args: { latitude: 40.7128, longitude: -74.0060 }
      },
      {
        type: 'tool-result',
        toolCallId: 'call-weather-123',
        result: { temperature: 72, condition: 'sunny' }
      },
      ...textToDeltas("It's currently 72°F and sunny in New York."),
      {
        type: 'finish',
        finishReason: 'stop',
        usage: { completionTokens: 20, promptTokens: 10 }
      }
    ];
  }
  
  // Document creation
  if (compareMessages(recentMessage, TEST_PROMPTS.USER_CREATE_DOCUMENT)) {
    return [
      ...textToDeltas("I'll create a document for you."),
      {
        type: 'tool-call',
        toolCallId: 'call-doc-123',
        toolName: 'createDocument',
        args: { title: 'Test Document', kind: 'text' }
      },
      {
        type: 'finish',
        finishReason: 'tool-calls'
      }
    ];
  }
  
  // Default response
  return [
    ...textToDeltas("I understand your request."),
    {
      type: 'finish',
      finishReason: 'stop',
      usage: { completionTokens: 5, promptTokens: 3 }
    }
  ];
}

function textToDeltas(text: string) {
  return text.split(' ').map(word => ({
    type: 'text-delta',
    textDelta: word + ' '
  }));
}
```

## Page Object Model

### Base Page Class

```typescript
// tests/pages/base.ts
import { Page, Locator } from '@playwright/test';

export class BasePage {
  constructor(protected page: Page) {}
  
  async waitForLoadState() {
    await this.page.waitForLoadState('networkidle');
  }
  
  async clickAndWait(locator: Locator, waitFor?: string) {
    await locator.click();
    if (waitFor) {
      await this.page.waitForSelector(waitFor);
    }
  }
}
```

### Chat Page Class

```typescript
// tests/pages/chat.ts
import { Page, expect } from '@playwright/test';
import { BasePage } from './base';

export class ChatPage extends BasePage {
  private messageInput = this.page.getByTestId('message-input');
  private sendButton = this.page.getByTestId('send-button');
  private messages = this.page.getByTestId('message');
  
  constructor(page: Page) {
    super(page);
  }
  
  async createNewChat() {
    await this.page.goto('/');
    await this.waitForLoadState();
  }
  
  async sendUserMessage(message: string) {
    await this.messageInput.fill(message);
    await this.sendButton.click();
    
    // Wait for message to appear
    await expect(this.messages.last()).toContainText(message);
  }
  
  async waitForAssistantResponse() {
    // Wait for assistant message to appear
    await this.page.waitForSelector('[data-testid="message-assistant"]');
  }
  
  async isGenerationComplete() {
    // Wait for streaming to complete
    await this.page.waitForFunction(() => {
      const status = document.querySelector('[data-testid="chat-status"]');
      return status?.textContent !== 'streaming';
    });
  }
  
  async getRecentAssistantMessage() {
    const assistantMessages = this.page.getByTestId('message-assistant');
    const lastMessage = assistantMessages.last();
    
    return {
      content: await lastMessage.getByTestId('message-content').textContent(),
      element: lastMessage
    };
  }
  
  async uploadFile(filePath: string) {
    const fileInput = this.page.getByTestId('file-input');
    await fileInput.setInputFiles(filePath);
    
    // Wait for upload to complete
    await this.page.waitForSelector('[data-testid="attachment-preview"]');
  }
  
  async openArtifact() {
    const artifactButton = this.page.getByTestId('artifact-button');
    await artifactButton.click();
    
    // Wait for artifact panel to open
    await this.page.waitForSelector('[data-testid="artifact-panel"]');
  }
  
  async getArtifactContent() {
    const artifactContent = this.page.getByTestId('artifact-content');
    return await artifactContent.textContent();
  }
}
```

### Authentication Page Class

```typescript
// tests/pages/auth.ts
import { Page } from '@playwright/test';
import { BasePage } from './base';

export class AuthPage extends BasePage {
  private emailInput = this.page.getByTestId('email-input');
  private passwordInput = this.page.getByTestId('password-input');
  private loginButton = this.page.getByTestId('login-button');
  private guestButton = this.page.getByTestId('guest-login-button');
  
  constructor(page: Page) {
    super(page);
  }
  
  async goto() {
    await this.page.goto('/login');
    await this.waitForLoadState();
  }
  
  async loginWithCredentials(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.loginButton.click();
    
    // Wait for redirect to chat
    await this.page.waitForURL('/');
  }
  
  async loginAsGuest() {
    await this.guestButton.click();
    
    // Wait for redirect to chat
    await this.page.waitForURL('/');
  }
}
```

## Test Examples

### Basic Chat Test

```typescript
// tests/e2e/chat.test.ts
import { test, expect } from '@playwright/test';
import { ChatPage } from '../pages/chat';

test.describe('Chat Functionality', () => {
  let chatPage: ChatPage;
  
  test.beforeEach(async ({ page }) => {
    chatPage = new ChatPage(page);
    await chatPage.createNewChat();
  });
  
  test('should send message and receive response', async () => {
    await chatPage.sendUserMessage('Hello, how are you?');
    await chatPage.waitForAssistantResponse();
    await chatPage.isGenerationComplete();
    
    const response = await chatPage.getRecentAssistantMessage();
    expect(response.content).toContain('understand');
  });
  
  test('should handle weather queries', async () => {
    await chatPage.sendUserMessage('What is the weather like?');
    await chatPage.waitForAssistantResponse();
    await chatPage.isGenerationComplete();
    
    const response = await chatPage.getRecentAssistantMessage();
    expect(response.content).toContain('weather');
    expect(response.content).toContain('72°F');
  });
  
  test('should create documents', async () => {
    await chatPage.sendUserMessage('Create a document about AI');
    await chatPage.waitForAssistantResponse();
    await chatPage.isGenerationComplete();
    
    // Check if artifact button appears
    const artifactButton = chatPage.page.getByTestId('artifact-button');
    await expect(artifactButton).toBeVisible();
    
    await chatPage.openArtifact();
    const artifactContent = await chatPage.getArtifactContent();
    expect(artifactContent).toBeTruthy();
  });
});
```

### Authentication Test

```typescript
// tests/e2e/auth.test.ts
import { test, expect } from '@playwright/test';
import { AuthPage } from '../pages/auth';
import { ChatPage } from '../pages/chat';

test.describe('Authentication', () => {
  test('should login as guest', async ({ page }) => {
    const authPage = new AuthPage(page);
    await authPage.goto();
    await authPage.loginAsGuest();
    
    // Verify redirect to chat
    await expect(page).toHaveURL('/');
    
    // Verify chat functionality works
    const chatPage = new ChatPage(page);
    await chatPage.sendUserMessage('Hello');
    await chatPage.waitForAssistantResponse();
  });
  
  test('should require authentication for protected routes', async ({ page }) => {
    // Try to access chat without authentication
    await page.goto('/chat/123');
    
    // Should redirect to login
    await expect(page).toHaveURL('/login');
  });
});
```

### Artifact Test

```typescript
// tests/e2e/artifacts.test.ts
import { test, expect } from '@playwright/test';
import { ChatPage } from '../pages/chat';

test.describe('Artifacts System', () => {
  let chatPage: ChatPage;
  
  test.beforeEach(async ({ page }) => {
    chatPage = new ChatPage(page);
    await chatPage.createNewChat();
  });
  
  test('should create text artifact', async () => {
    await chatPage.sendUserMessage('Write an essay about technology');
    await chatPage.waitForAssistantResponse();
    await chatPage.isGenerationComplete();
    
    await chatPage.openArtifact();
    const content = await chatPage.getArtifactContent();
    expect(content).toContain('technology');
  });
  
  test('should create code artifact', async () => {
    await chatPage.sendUserMessage('Write Python code to calculate fibonacci');
    await chatPage.waitForAssistantResponse();
    await chatPage.isGenerationComplete();
    
    await chatPage.openArtifact();
    
    // Check for code editor
    const codeEditor = chatPage.page.getByTestId('code-editor');
    await expect(codeEditor).toBeVisible();
    
    // Check for run button
    const runButton = chatPage.page.getByTestId('run-code-button');
    await expect(runButton).toBeVisible();
  });
});
```

## Test Configuration

### Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry'
  },
  
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ],
  
  webServer: {
    command: 'pnpm dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI
  }
});
```

### Test Environment Setup

```typescript
// tests/fixtures.ts
import { test as base } from '@playwright/test';
import { AuthPage } from './pages/auth';
import { ChatPage } from './pages/chat';

type TestFixtures = {
  authPage: AuthPage;
  chatPage: ChatPage;
  authenticatedPage: ChatPage;
};

export const test = base.extend<TestFixtures>({
  authPage: async ({ page }, use) => {
    await use(new AuthPage(page));
  },
  
  chatPage: async ({ page }, use) => {
    await use(new ChatPage(page));
  },
  
  authenticatedPage: async ({ page }, use) => {
    const authPage = new AuthPage(page);
    await authPage.goto();
    await authPage.loginAsGuest();
    
    const chatPage = new ChatPage(page);
    await use(chatPage);
  }
});
```

## Running Tests

### Command Line

```bash
# Run all tests
pnpm test

# Run specific test file
pnpm exec playwright test chat.test.ts

# Run tests in headed mode
pnpm exec playwright test --headed

# Run tests in specific browser
pnpm exec playwright test --project=chromium

# Generate test report
pnpm exec playwright show-report
```

### Environment Variables

```bash
# Set test environment
export PLAYWRIGHT=true
export NODE_ENV=test

# Run tests
pnpm test
```

## Best Practices

### Test Organization
1. **Page Object Model** for maintainable test code
2. **Descriptive test names** that explain the behavior
3. **Setup and teardown** in beforeEach/afterEach hooks
4. **Test isolation** - each test should be independent

### Mock Strategy
1. **Deterministic responses** for reliable testing
2. **Comprehensive scenarios** covering different use cases
3. **Error simulation** for error handling tests
4. **Performance testing** with controlled delays

### Debugging
1. **Screenshots** on test failures
2. **Video recording** for complex interactions
3. **Console logs** for debugging test issues
4. **Trace viewer** for detailed test analysis

This testing strategy ensures comprehensive coverage of the AI Chatbot's functionality while maintaining fast, reliable test execution.
