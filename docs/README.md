# AI Chatbot Documentation

This documentation provides comprehensive guidance for understanding, customizing, and extending the AI Chatbot project built with the Chat SDK.

## Table of Contents

### Getting Started

- [Architecture Overview](./architecture.md) - System design and component relationships
- [Configuration Guide](./configuration.md) - Environment setup and customization
- [Database Schema](./database.md) - Schema design and migration patterns

### Development

- [API Reference](./api-reference.md) - Hooks, components, and endpoints
- [Artifacts System](./artifacts.md) - Creating and customizing artifacts
- [Multi-Provider Setup](./multi-provider-setup.md) - Configure multiple AI providers
- [Testing Guide](./testing.md) - E2E testing with Playwright

### Operations

- [Deployment Guide](./deployment.md) - Production deployment best practices
- [Troubleshooting](./troubleshooting.md) - Common issues and solutions

## Quick Reference

### Key Technologies

- **Framework**: Next.js 14 with App Router
- **AI Integration**: Vercel AI SDK v5
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Auth.js (NextAuth)
- **Styling**: Tailwind CSS + shadcn/ui
- **Testing**: Playwright for E2E testing

### Project Structure

```
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (chat)/            # Main chat interface
│   └── api/               # API routes
├── artifacts/             # Artifact type definitions
├── components/            # React components
├── hooks/                 # Custom React hooks
├── lib/                   # Utility libraries
└── docs/                  # Project documentation
```

### Environment Variables

```bash
AUTH_SECRET=****           # Authentication secret
XAI_API_KEY=****          # xAI API key
POSTGRES_URL=****         # PostgreSQL connection
BLOB_READ_WRITE_TOKEN=**** # Vercel Blob storage
REDIS_URL=****            # Redis (optional)
```

### Development Commands

```bash
pnpm dev                  # Start development server
pnpm build               # Build for production
pnpm db:migrate          # Run database migrations
pnpm db:studio           # Open database studio
pnpm test                # Run E2E tests
pnpm lint                # Lint and format code
```

## Contributing

When making changes to the project:

1. **Follow established patterns** - Review existing code and documentation
2. **Update documentation** - Keep docs in sync with code changes
3. **Test thoroughly** - Run E2E tests and add new tests for new features
4. **Follow conventions** - Use TypeScript, proper error handling, and consistent styling

## Support

- **GitHub Issues**: [Report bugs and request features](https://github.com/vercel/ai-chatbot/issues)
- **Chat SDK Docs**: [Official documentation](https://chat-sdk.dev/docs)
- **AI SDK Docs**: [Vercel AI SDK documentation](https://sdk.vercel.ai/docs)
