# Multi-Provider AI Setup Guide

This guide covers the setup and configuration of multiple AI providers (xAI, Google, and Mistral) in the AI Chatbot project.

## Overview

The chatbot now supports multiple AI providers, allowing you to:
- Use different models for different tasks
- Provide fallback options when certain models are unavailable
- Give users choice in model selection
- Optimize costs by using appropriate models for different use cases

## Supported Providers

### 1. xAI (Default)
- **Models**: Grok 2 Vision, Grok 3 Mini
- **Capabilities**: Text generation, vision, reasoning
- **API Key**: `XAI_API_KEY`
- **Get Key**: [xAI Console](https://console.x.ai/)

### 2. Google AI
- **Models**: Gemini 1.5 Pro, Gemini 1.5 Flash, Gemini 1.5 Flash 8B
- **Capabilities**: Text generation, multimodal
- **API Key**: `GOOGLE_GENERATIVE_AI_API_KEY`
- **Get Key**: [Google AI Studio](https://aistudio.google.com/app/apikey)

### 3. Mistral AI
- **Models**: Mistral Large, Mistra<PERSON> Small, Codestral, Pixtral 12B
- **Capabilities**: Text generation, code generation, vision
- **API Key**: `MISTRAL_API_KEY`
- **Get Key**: [Mistral Console](https://console.mistral.ai/)

## Environment Setup

### Required Environment Variables

Add these to your `.env.local` file:

```bash
# xAI (Default provider)
XAI_API_KEY=your-xai-api-key

# Google AI (Optional)
GOOGLE_GENERATIVE_AI_API_KEY=your-google-api-key

# Mistral AI (Optional)
MISTRAL_API_KEY=your-mistral-api-key
```

### Getting API Keys

#### xAI API Key
1. Visit [xAI Console](https://console.x.ai/)
2. Sign up or log in
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your environment variables

#### Google AI API Key
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the key to your environment variables

#### Mistral AI API Key
1. Visit [Mistral Console](https://console.mistral.ai/)
2. Sign up or log in
3. Go to API Keys section
4. Generate a new API key
5. Copy the key to your environment variables

## Model Configuration

### Available Models

The system automatically detects which providers are available based on API keys:

#### xAI Models
- **Grok 2 Vision** (`chat-model`): Multimodal model with vision capabilities
- **Grok 3 Mini** (`chat-model-reasoning`): Reasoning model with advanced problem-solving

#### Google Models
- **Gemini 1.5 Pro** (`google-gemini-pro`): Most capable model for complex tasks
- **Gemini 1.5 Flash** (`google-gemini-flash`): Fast and efficient model
- **Gemini 1.5 Flash 8B** (`google-gemini-flash-8b`): Lightweight model for simple tasks

#### Mistral Models
- **Mistral Large** (`mistral-large`): Most powerful model for complex reasoning
- **Mistral Small** (`mistral-small`): Efficient model for everyday tasks
- **Codestral** (`mistral-codestral`): Specialized model for code generation
- **Pixtral 12B** (`mistral-pixtral`): Multimodal model with vision capabilities

### User Access Levels

#### Guest Users (Limited Access)
- Maximum 20 messages per day
- Access to basic models:
  - Grok 2 Vision (if xAI key available)
  - Gemini 1.5 Flash 8B (if Google key available)
  - Mistral Small (if Mistral key available)

#### Regular Users (Full Access)
- Maximum 100 messages per day
- Access to all available models based on configured API keys

## Fallback System

The system includes intelligent fallback logic:

1. **Primary**: Requested model (if available)
2. **Fallback 1**: Grok 2 Vision (xAI)
3. **Fallback 2**: Gemini 1.5 Flash 8B (Google)
4. **Fallback 3**: Mistral Small (Mistral)

If no models are available, the system will throw an error prompting to check API key configuration.

## Usage Examples

### Basic Setup (xAI Only)
```bash
# Minimum setup - only xAI
XAI_API_KEY=your-xai-key
```

### Multi-Provider Setup
```bash
# Full setup - all providers
XAI_API_KEY=your-xai-key
GOOGLE_GENERATIVE_AI_API_KEY=your-google-key
MISTRAL_API_KEY=your-mistral-key
```

### Google + Mistral (No xAI)
```bash
# Alternative setup without xAI
GOOGLE_GENERATIVE_AI_API_KEY=your-google-key
MISTRAL_API_KEY=your-mistral-key
```

## Model Selection UI

Users can select models through the model selector dropdown in the chat interface. The dropdown automatically shows only available models based on configured API keys.

### Model Grouping
Models are grouped by provider in the UI:
- **xAI Models**: Grok series
- **Google Models**: Gemini series  
- **Mistral Models**: Mistral series

## Cost Optimization

### Model Recommendations by Use Case

#### General Chat
- **Budget**: Gemini 1.5 Flash 8B, Mistral Small
- **Balanced**: Gemini 1.5 Flash, Grok 2 Vision
- **Premium**: Gemini 1.5 Pro, Mistral Large

#### Code Generation
- **Specialized**: Codestral (Mistral)
- **General**: Grok 2 Vision, Gemini 1.5 Pro

#### Complex Reasoning
- **Best**: Grok 3 Mini (Reasoning), Mistral Large
- **Good**: Gemini 1.5 Pro

#### Vision Tasks
- **Available**: Grok 2 Vision, Pixtral 12B

## Troubleshooting

### Common Issues

#### No Models Available
**Error**: "No AI models are available"
**Solution**: Check that at least one API key is configured correctly

#### Model Not Found
**Error**: Model selection doesn't work
**Solution**: Verify API key is valid and model is supported

#### Fallback Warnings
**Warning**: "Model X not available, falling back to Y"
**Solution**: This is normal behavior when requested model isn't available

### Debugging

Check model availability:
```typescript
import { isModelAvailable, getAvailableModels } from '@/lib/ai/config';

console.log('Available models:', getAvailableModels());
console.log('Is Gemini Pro available:', isModelAvailable('google-gemini-pro'));
```

### API Key Validation

The system validates API keys on startup and shows warnings for missing keys:
```
Warning: Google API key not found. Google models will not be available.
Warning: Mistral API key not found. Mistral models will not be available.
```

## Best Practices

1. **Start Simple**: Begin with one provider and add others as needed
2. **Monitor Costs**: Different models have different pricing structures
3. **Use Appropriate Models**: Match model capabilities to use case requirements
4. **Test Fallbacks**: Ensure fallback logic works by temporarily removing API keys
5. **User Communication**: The UI clearly shows which provider each model uses

## Security Notes

- Store API keys securely in environment variables
- Never commit API keys to version control
- Use different API keys for development and production
- Monitor API usage to detect unusual activity
- Rotate API keys regularly for security

This multi-provider setup provides flexibility, redundancy, and cost optimization while maintaining a seamless user experience.
