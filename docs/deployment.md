# Deployment Guide

This document covers deployment strategies, configuration, and best practices for the AI Chatbot project in production environments.

## Deployment Options

### 1. Vercel (Recommended)

Vercel provides the optimal deployment experience with one-click setup and automatic optimizations.

#### One-Click Deploy

1. Use the [deployment link](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fai-chatbot) for instant setup
2. Connect your GitHub account and create a new repository
3. Configure required integrations:
   - **xAI Integration**: For AI model access
   - **Neon Postgres**: For database storage
   - **Vercel Blob**: For file storage
4. Set environment variables automatically through integrations
5. Deploy and start using immediately

#### Manual Vercel Setup

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Link project to Vercel
vercel link

# Set environment variables
vercel env add AUTH_SECRET
vercel env add XAI_API_KEY
vercel env add POSTGRES_URL
vercel env add BLOB_READ_WRITE_TOKEN

# Deploy
vercel --prod
```

### 2. Docker Deployment

For self-hosted environments or other cloud providers.

#### Dockerfile

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json pnpm-lock.yaml* ./
RUN corepack enable pnpm && pnpm i --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN corepack enable pnpm && pnpm build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - AUTH_SECRET=${AUTH_SECRET}
      - XAI_API_KEY=${XAI_API_KEY}
      - POSTGRES_URL=${POSTGRES_URL}
      - BLOB_READ_WRITE_TOKEN=${BLOB_READ_WRITE_TOKEN}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=chatbot
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 3. Other Cloud Providers

#### AWS (using AWS App Runner)

```yaml
# apprunner.yaml
version: 1.0
runtime: nodejs18
build:
  commands:
    build:
      - corepack enable pnpm
      - pnpm install --frozen-lockfile
      - pnpm build
run:
  runtime-version: 18
  command: pnpm start
  network:
    port: 3000
    env: PORT
  env:
    - name: NODE_ENV
      value: production
```

#### Google Cloud Platform (Cloud Run)

```yaml
# cloudbuild.yaml
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/ai-chatbot', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ai-chatbot']
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'ai-chatbot'
      - '--image'
      - 'gcr.io/$PROJECT_ID/ai-chatbot'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
```

## Environment Configuration

### Production Environment Variables

```bash
# Core Configuration
NODE_ENV=production
AUTH_SECRET=your-production-secret-32-chars
XAI_API_KEY=your-production-xai-key

# Database
POSTGRES_URL=postgresql://user:pass@host:port/db
# For connection pooling in production
DATABASE_URL=postgresql://user:pass@host:port/db?pgbouncer=true

# Storage
BLOB_READ_WRITE_TOKEN=your-production-blob-token

# Caching (Optional but recommended)
REDIS_URL=redis://user:pass@host:port
# Or Vercel KV
KV_URL=your-vercel-kv-url

# Monitoring (Optional)
SENTRY_DSN=your-sentry-dsn
VERCEL_ANALYTICS_ID=your-analytics-id
```

### Security Configuration

```bash
# Additional security headers
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-nextauth-secret

# Rate limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000  # 15 minutes in ms

# CORS configuration
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

## Database Setup

### Migration in Production

```bash
# Run migrations before deployment
pnpm db:migrate

# Or include in build process
# package.json
{
  "scripts": {
    "build": "tsx lib/db/migrate && next build"
  }
}
```

### Connection Pooling

```typescript
// lib/db/index.ts (Production)
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

const connection = postgres(process.env.POSTGRES_URL!, {
  max: 20,                    // Maximum connections
  idle_timeout: 20,           // Close idle connections after 20s
  connect_timeout: 10,        // Connection timeout
  ssl: process.env.NODE_ENV === 'production' ? 'require' : false
});

export const db = drizzle(connection);
```

## Performance Optimization

### Next.js Configuration

```typescript
// next.config.ts (Production)
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Enable experimental features
  experimental: {
    ppr: true,                // Partial Prerendering
    reactCompiler: true       // React Compiler
  },
  
  // Optimize images
  images: {
    formats: ['image/webp', 'image/avif'],
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh'
      }
    ]
  },
  
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          }
        ]
      }
    ];
  },
  
  // Compression
  compress: true,
  
  // Remove powered by header
  poweredByHeader: false,
  
  // Optimize bundle
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false
      };
    }
    return config;
  }
};

export default nextConfig;
```

### Caching Strategy

```typescript
// lib/cache.ts
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.REDIS_URL!,
  token: process.env.REDIS_TOKEN!
});

export async function getCachedResponse(key: string) {
  try {
    return await redis.get(key);
  } catch (error) {
    console.error('Cache get error:', error);
    return null;
  }
}

export async function setCachedResponse(
  key: string, 
  value: any, 
  ttl: number = 3600
) {
  try {
    await redis.setex(key, ttl, JSON.stringify(value));
  } catch (error) {
    console.error('Cache set error:', error);
  }
}
```

## Monitoring & Observability

### Error Tracking

```typescript
// lib/monitoring.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1,
  
  beforeSend(event) {
    // Filter out sensitive data
    if (event.request?.headers) {
      delete event.request.headers.authorization;
      delete event.request.headers.cookie;
    }
    return event;
  }
});
```

### Analytics

```typescript
// lib/analytics.ts
import { Analytics } from '@vercel/analytics/react';

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      <Analytics />
    </>
  );
}
```

### Health Checks

```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET() {
  try {
    // Check database connection
    await db.execute('SELECT 1');
    
    // Check external services
    const checks = {
      database: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || 'unknown'
    };
    
    return NextResponse.json(checks);
  } catch (error) {
    return NextResponse.json(
      { error: 'Health check failed' },
      { status: 500 }
    );
  }
}
```

## Security Best Practices

### Rate Limiting

```typescript
// lib/rate-limit.ts
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.REDIS_URL!,
  token: process.env.REDIS_TOKEN!
});

export async function rateLimit(
  identifier: string,
  limit: number = 10,
  window: number = 60
) {
  const key = `rate_limit:${identifier}`;
  const current = await redis.incr(key);
  
  if (current === 1) {
    await redis.expire(key, window);
  }
  
  return {
    success: current <= limit,
    remaining: Math.max(0, limit - current),
    reset: Date.now() + (window * 1000)
  };
}
```

### Input Validation

```typescript
// lib/validation.ts
import { z } from 'zod';

export const chatMessageSchema = z.object({
  id: z.string().uuid(),
  message: z.object({
    role: z.enum(['user', 'assistant']),
    parts: z.array(z.any()),
    attachments: z.array(z.any()).default([])
  }),
  selectedChatModel: z.string(),
  selectedVisibilityType: z.enum(['public', 'private'])
});

export function validateChatMessage(data: unknown) {
  return chatMessageSchema.parse(data);
}
```

## Backup & Recovery

### Database Backups

```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backup_${DATE}.sql"

pg_dump $POSTGRES_URL > $BACKUP_FILE
aws s3 cp $BACKUP_FILE s3://your-backup-bucket/

# Clean up local file
rm $BACKUP_FILE
```

### Disaster Recovery

1. **Database**: Regular automated backups to cloud storage
2. **Files**: Vercel Blob provides built-in redundancy
3. **Code**: Git repository with multiple remotes
4. **Configuration**: Environment variables backed up securely

## Scaling Considerations

### Horizontal Scaling
- **Serverless Functions**: Automatic scaling with Vercel
- **Database**: Connection pooling and read replicas
- **CDN**: Global content distribution
- **Caching**: Redis for session and response caching

### Vertical Scaling
- **Memory**: Optimize bundle size and memory usage
- **CPU**: Efficient algorithms and caching
- **I/O**: Database query optimization
- **Network**: Compression and CDN usage

This deployment guide ensures a robust, secure, and scalable production environment for the AI Chatbot application.
