# API Reference

This document provides comprehensive reference for all APIs, hooks, components, and endpoints in the AI Chatbot project.

## React Hooks

### `useChat`

The primary hook for chat functionality, provided by the AI SDK.

```typescript
const {
  messages,           // Array of ChatMessage objects
  setMessages,        // Function to update messages
  input,             // Current input value
  setInput,          // Function to update input
  status,            // 'idle' | 'loading' | 'streaming'
  sendMessage,       // Function to send message
  stop,              // Function to stop generation
  regenerate,        // Function to regenerate last message
  experimental_resume // Function to resume interrupted streams
} = useChat({
  api: '/api/chat',
  id: chatId,
  onFinish: (message) => { /* save to database */ }
});
```

**Parameters:**
- `api` (string): API endpoint for chat requests
- `id` (string): Unique chat identifier
- `onFinish` (function): Callback when message generation completes
- `initialMessages` (array): Initial message history

### `useArtifact`

Custom hook for managing artifact state.

```typescript
const {
  artifact,          // Current artifact state
  setArtifact,       // Function to update artifact
  metadata,          // Artifact metadata
  setMetadata        // Function to update metadata
} = useArtifact();
```

**Artifact State:**
```typescript
interface UIArtifact {
  documentId: string;
  content: string;
  kind: 'text' | 'code' | 'image' | 'sheet';
  title: string;
  status: 'idle' | 'streaming' | 'complete';
  isVisible: boolean;
  boundingBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
}
```

### `useArtifactSelector`

Selector hook for specific artifact properties.

```typescript
const selectedValue = useArtifactSelector<Selected>(
  (artifact: UIArtifact) => artifact.content
);
```

## Data Types

### ChatMessage

```typescript
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  parts: MessagePart[];
  attachments: Attachment[];
  createdAt: Date;
}
```

### MessagePart

```typescript
type MessagePart = 
  | { type: 'text'; text: string }
  | { type: 'tool-invocation'; toolInvocation: ToolInvocation }
  | { type: 'tool-result'; toolResult: ToolResult }
  | { type: 'reasoning'; reasoning: string };
```

### ToolInvocation

```typescript
interface ToolInvocation {
  toolName: string;
  toolCallId: string;
  state: 'call' | 'result';
  args?: Record<string, any>;
  result?: any;
}
```

### Attachment

```typescript
interface Attachment {
  name: string;
  contentType: string;
  size: number;
  url: string;
}
```

## API Endpoints

### `/api/chat` (POST)

Main chat endpoint for processing messages.

**Request:**
```typescript
{
  id: string;                    // Chat ID
  message: ChatMessage;          // User message
  selectedChatModel: string;     // Model identifier
  selectedVisibilityType: 'public' | 'private';
}
```

**Response:**
- Streaming response with text deltas and tool calls
- Server-Sent Events format
- Automatic message persistence

**Available Tools:**
- `getWeather`: Weather information retrieval
- `createDocument`: Artifact creation
- `updateDocument`: Artifact modification
- `requestSuggestions`: Content improvement suggestions

### `/api/chat` (GET)

Resume interrupted chat streams.

**Query Parameters:**
- `chatId` (string): Chat identifier to resume

**Response:**
- Resumed stream if active
- Empty response if no active stream

### `/api/document` (GET)

Retrieve document by ID.

**Query Parameters:**
- `id` (string): Document identifier

**Response:**
```typescript
{
  id: string;
  title: string;
  content: string;
  kind: 'text' | 'code' | 'image' | 'sheet';
  createdAt: string;
  userId: string;
}
```

### `/api/document` (POST)

Create or update document.

**Query Parameters:**
- `id` (string): Document identifier

**Request Body:**
```typescript
{
  content: string;
  title: string;
  kind: 'text' | 'code' | 'image' | 'sheet';
}
```

### `/api/document` (DELETE)

Delete document by ID.

**Query Parameters:**
- `id` (string): Document identifier

### `/api/history` (GET)

Retrieve paginated chat history.

**Query Parameters:**
- `limit` (number): Number of chats to retrieve (default: 10)
- `starting_after` (string): Pagination cursor for next page
- `ending_before` (string): Pagination cursor for previous page

**Response:**
```typescript
Array<{
  id: string;
  title: string;
  createdAt: string;
  visibility: 'public' | 'private';
}>
```

### `/api/files/upload` (POST)

Upload file attachments.

**Request:**
- Multipart form data with file

**Response:**
```typescript
{
  url: string;        // Secure file URL
  name: string;       // Original filename
  size: number;       // File size in bytes
  contentType: string; // MIME type
}
```

## Core Components

### `<Chat>`

Main chat interface component.

```typescript
interface ChatProps {
  id: string;
  initialMessages?: ChatMessage[];
  selectedModelId?: string;
}
```

### `<Artifact>`

Artifact display and interaction component.

```typescript
interface ArtifactProps {
  chatId: string;
  input: string;
  setInput: (input: string) => void;
  status: ChatStatus;
  stop: () => void;
  attachments: Attachment[];
  setAttachments: (attachments: Attachment[]) => void;
  sendMessage: (message: ChatMessage) => void;
  messages: ChatMessage[];
  setMessages: (messages: ChatMessage[]) => void;
  regenerate: () => void;
  votes?: Vote[];
  isReadonly: boolean;
  selectedVisibilityType: VisibilityType;
}
```

### `<MultimodalInput>`

Input component supporting text and file attachments.

```typescript
interface MultimodalInputProps {
  chatId: string;
  input: string;
  setInput: (input: string) => void;
  status: ChatStatus;
  stop: () => void;
  attachments: Attachment[];
  setAttachments: (attachments: Attachment[]) => void;
  messages: ChatMessage[];
  sendMessage: (message: ChatMessage) => void;
  setMessages: (messages: ChatMessage[]) => void;
  selectedVisibilityType: VisibilityType;
  className?: string;
}
```

### `<Messages>`

Message list display component.

```typescript
interface MessagesProps {
  chatId: string;
  messages: ChatMessage[];
  setMessages: (messages: ChatMessage[]) => void;
  regenerate: () => void;
  votes?: Vote[];
  isReadonly: boolean;
}
```

## Utility Functions

### Database Queries

```typescript
// Chat operations
getChatById(params: { id: string }): Promise<Chat | null>
getChatsByUserId(params: { id: string; limit?: number }): Promise<Chat[]>
saveChat(params: { id: string; userId: string; title: string }): Promise<void>
deleteChatById(params: { id: string }): Promise<void>

// Message operations
getMessagesByChatId(params: { id: string }): Promise<DBMessage[]>
saveMessages(params: { messages: DBMessage[] }): Promise<void>
getMessageById(params: { id: string }): Promise<DBMessage | null>

// Document operations
getDocumentById(params: { id: string }): Promise<Document | null>
saveDocument(params: SaveDocumentProps): Promise<void>
getDocumentsByUserId(params: { userId: string }): Promise<Document[]>

// User operations
getUser(email: string): Promise<User[]>
createGuestUser(): Promise<User[]>
```

### Utility Helpers

```typescript
// Message conversion
convertToUIMessages(messages: DBMessage[]): ChatMessage[]
convertToModelMessages(messages: ChatMessage[]): ModelMessage[]

// ID generation
generateUUID(): string

// Text processing
sanitizeText(text: string): string

// Error handling
fetchWithErrorHandlers(url: string, options?: RequestInit): Promise<Response>
```

## Error Handling

### ChatSDKError

Custom error class for consistent error handling.

```typescript
class ChatSDKError extends Error {
  constructor(
    public code: string,
    message?: string
  ) {
    super(message);
  }
  
  toResponse(): Response {
    return new Response(
      JSON.stringify({ error: this.code, message: this.message }),
      { status: this.getStatusCode(), headers: { 'Content-Type': 'application/json' } }
    );
  }
}
```

**Error Codes:**
- `unauthorized:chat` - Authentication required
- `forbidden:chat` - Access denied
- `rate_limit:chat` - Rate limit exceeded
- `bad_request:api` - Invalid request parameters
- `not_found:document` - Document not found

This API reference provides the foundation for understanding and extending the AI Chatbot's functionality.
