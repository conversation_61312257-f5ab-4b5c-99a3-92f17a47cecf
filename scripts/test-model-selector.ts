#!/usr/bin/env tsx

/**
 * Test script to verify model selector data is working correctly
 * Run with: pnpm exec tsx scripts/test-model-selector.ts
 */

import { config } from 'dotenv';
import { chatModels, availableChatModels } from '../lib/ai/models';
import { entitlementsByUserType } from '../lib/ai/entitlements';
import { isModelAvailable } from '../lib/ai/config';

// Load environment variables
config({ path: '.env.local' });

console.log('🎛️  Model Selector Data Test\n');

// Test 1: Check that models are available for UI display
console.log('📋 UI Models (Always Shown):');
if (chatModels.length === 0) {
  console.log('  ❌ No models configured for UI display');
} else {
  console.log(`  ✅ ${chatModels.length} models configured for UI display:`);
  chatModels.forEach(model => {
    const available = isModelAvailable(model.id);
    const status = available ? '✅ Available' : '⚠️  Requires API Key';
    console.log(`    ${status} ${model.name} (${model.provider})`);
  });
}
console.log();

// Test 2: Check runtime available models
console.log('⚡ Runtime Available Models:');
if (availableChatModels.length === 0) {
  console.log('  ❌ No models available at runtime - configure API keys');
} else {
  console.log(`  ✅ ${availableChatModels.length} models available at runtime:`);
  availableChatModels.forEach(model => {
    console.log(`    ✅ ${model.name} (${model.provider})`);
  });
}
console.log();

// Test 3: Check user entitlements
console.log('👥 User Entitlements (UI Models):');
Object.entries(entitlementsByUserType).forEach(([userType, entitlements]) => {
  console.log(`  ${userType}:`);
  console.log(`    Max messages/day: ${entitlements.maxMessagesPerDay}`);
  console.log(`    Entitled models: ${entitlements.availableChatModelIds.length}`);
  
  entitlements.availableChatModelIds.forEach(modelId => {
    const model = chatModels.find(m => m.id === modelId);
    const available = isModelAvailable(modelId);
    const status = available ? '✅' : '⚠️ ';
    if (model) {
      console.log(`      ${status} ${model.name} (${model.provider})`);
    } else {
      console.log(`      ❌ Unknown model: ${modelId}`);
    }
  });
  console.log();
});

// Test 4: Model selector simulation
console.log('🎛️  Model Selector Simulation:');
const userType = 'regular'; // Simulate regular user
const { availableChatModelIds } = entitlementsByUserType[userType];

const userVisibleModels = chatModels.filter((chatModel) =>
  availableChatModelIds.includes(chatModel.id),
);

console.log(`  User type: ${userType}`);
console.log(`  Models visible in selector: ${userVisibleModels.length}`);

if (userVisibleModels.length === 0) {
  console.log('  ❌ Model selector would be empty');
} else {
  console.log('  ✅ Model selector would show:');
  
  // Group by provider like the actual component
  const groupedModels = userVisibleModels.reduce((acc, model) => {
    if (!acc[model.provider]) {
      acc[model.provider] = [];
    }
    acc[model.provider].push(model);
    return acc;
  }, {} as Record<string, typeof userVisibleModels>);

  Object.entries(groupedModels).forEach(([provider, models]) => {
    console.log(`    📁 ${provider.toUpperCase()}:`);
    models.forEach(model => {
      const available = isModelAvailable(model.id);
      const status = available ? '✅ Ready' : '⚠️  API Key Required';
      console.log(`      ${status} ${model.name}`);
    });
  });
}

console.log('\n🎉 Model selector data test complete!');
console.log('\nThe model selector should now show all models with appropriate status indicators.');
console.log('Models without API keys will be shown but disabled with "API Key Required" badges.');
