#!/usr/bin/env tsx

/**
 * Test script to verify AI provider configuration
 * Run with: pnpm exec tsx scripts/test-providers.ts
 */

import { config } from 'dotenv';
import {
  providerAvailability,
  getAvailableModels,
  isModelAvailable,
  getFallbackModel,
} from '../lib/ai/config';
import { chatModels } from '../lib/ai/models';
import { entitlementsByUserType } from '../lib/ai/entitlements';

// Load environment variables
config({ path: '.env.local' });

console.log('🤖 AI Provider Configuration Test\n');

// Test 1: Check API key availability
console.log('📋 API Key Status:');
console.log(
  `  xAI: ${providerAvailability.xai ? '✅ Available' : '❌ Missing'}`,
);
console.log(
  `  Google: ${providerAvailability.google ? '✅ Available' : '❌ Missing'}`,
);
console.log(
  `  Mistral: ${providerAvailability.mistral ? '✅ Available' : '❌ Missing'}`,
);
console.log();

// Test 2: Available models
const availableModels = getAvailableModels();
console.log('🎯 Available Models:');
if (availableModels.length === 0) {
  console.log('  ❌ No models available - check your API keys');
} else {
  availableModels.forEach((modelId) => {
    const model = chatModels.find((m) => m.id === modelId);
    if (model) {
      console.log(`  ✅ ${model.name} (${model.provider})`);
    }
  });
}
console.log();

// Test 3: Fallback logic
console.log('🔄 Fallback Logic Test:');
const testModels = [
  'google-gemini-2-flash',
  'chat-model',
  'google-gemini-pro',
  'mistral-large',
  'non-existent-model',
];

testModels.forEach((modelId) => {
  try {
    const fallback = getFallbackModel(modelId);
    const available = isModelAvailable(modelId);
    console.log(
      `  ${modelId}: ${available ? '✅ Available' : `⚠️  Fallback to ${fallback}`}`,
    );
  } catch (error) {
    console.log(`  ${modelId}: ❌ ${error.message}`);
  }
});
console.log();

// Test 4: User entitlements
console.log('👥 User Entitlements:');
Object.entries(entitlementsByUserType).forEach(([userType, entitlements]) => {
  console.log(`  ${userType}:`);
  console.log(`    Max messages/day: ${entitlements.maxMessagesPerDay}`);
  console.log(
    `    Available models: ${entitlements.availableChatModelIds.length}`,
  );
  entitlements.availableChatModelIds.forEach((modelId) => {
    const model = chatModels.find((m) => m.id === modelId);
    if (model) {
      console.log(`      - ${model.name} (${model.provider})`);
    }
  });
  console.log();
});

// Test 5: Configuration summary
console.log('📊 Configuration Summary:');
console.log(`  Total models configured: ${chatModels.length}`);
console.log(`  Available models: ${availableModels.length}`);
console.log(
  `  Providers with API keys: ${Object.values(providerAvailability).filter(Boolean).length}/3`,
);

if (availableModels.length === 0) {
  console.log('\n❌ Setup incomplete! Please configure at least one API key:');
  console.log('  - XAI_API_KEY for xAI models');
  console.log('  - GOOGLE_GENERATIVE_AI_API_KEY for Google models');
  console.log('  - MISTRAL_API_KEY for Mistral models');
} else {
  console.log('\n✅ Setup complete! Your AI chatbot is ready to use.');
}
