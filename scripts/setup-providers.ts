#!/usr/bin/env tsx

/**
 * Interactive setup script for AI providers
 * Run with: pnpm exec tsx scripts/setup-providers.ts
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🚀 AI Chatbot Provider Setup\n');
  console.log('This script will help you configure API keys for different AI providers.\n');

  const envPath = join(process.cwd(), '.env.local');
  let envContent = '';

  // Read existing .env.local if it exists
  if (existsSync(envPath)) {
    envContent = readFileSync(envPath, 'utf-8');
    console.log('📄 Found existing .env.local file\n');
  } else {
    console.log('📄 Creating new .env.local file\n');
  }

  // Helper function to update or add environment variable
  function updateEnvVar(key: string, value: string) {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, `${key}=${value}`);
    } else {
      envContent += `\n${key}=${value}`;
    }
  }

  console.log('Configure AI Providers (press Enter to skip):\n');

  // xAI Configuration
  console.log('🔥 xAI (Grok Models)');
  console.log('   Get your API key at: https://console.x.ai/');
  const xaiKey = await question('   Enter xAI API key: ');
  if (xaiKey.trim()) {
    updateEnvVar('XAI_API_KEY', xaiKey.trim());
    console.log('   ✅ xAI API key configured\n');
  } else {
    console.log('   ⏭️  Skipped xAI configuration\n');
  }

  // Google AI Configuration
  console.log('🧠 Google AI (Gemini Models)');
  console.log('   Get your API key at: https://aistudio.google.com/app/apikey');
  const googleKey = await question('   Enter Google AI API key: ');
  if (googleKey.trim()) {
    updateEnvVar('GOOGLE_GENERATIVE_AI_API_KEY', googleKey.trim());
    console.log('   ✅ Google AI API key configured\n');
  } else {
    console.log('   ⏭️  Skipped Google AI configuration\n');
  }

  // Mistral AI Configuration
  console.log('🌟 Mistral AI (Mistral Models)');
  console.log('   Get your API key at: https://console.mistral.ai/');
  const mistralKey = await question('   Enter Mistral AI API key: ');
  if (mistralKey.trim()) {
    updateEnvVar('MISTRAL_API_KEY', mistralKey.trim());
    console.log('   ✅ Mistral AI API key configured\n');
  } else {
    console.log('   ⏭️  Skipped Mistral AI configuration\n');
  }

  // Other required variables
  console.log('🔐 Other Required Configuration:\n');

  // AUTH_SECRET
  if (!envContent.includes('AUTH_SECRET=')) {
    console.log('🔑 Authentication Secret');
    console.log('   Generate at: https://generate-secret.vercel.app/32');
    const authSecret = await question('   Enter AUTH_SECRET (32+ characters): ');
    if (authSecret.trim()) {
      updateEnvVar('AUTH_SECRET', authSecret.trim());
      console.log('   ✅ AUTH_SECRET configured\n');
    } else {
      console.log('   ⚠️  AUTH_SECRET is required for authentication\n');
    }
  }

  // Database URL
  if (!envContent.includes('POSTGRES_URL=')) {
    console.log('🗄️  PostgreSQL Database');
    console.log('   Get a free database at: https://vercel.com/marketplace/neon');
    const dbUrl = await question('   Enter POSTGRES_URL: ');
    if (dbUrl.trim()) {
      updateEnvVar('POSTGRES_URL', dbUrl.trim());
      console.log('   ✅ Database URL configured\n');
    } else {
      console.log('   ⚠️  Database URL is required for data persistence\n');
    }
  }

  // Blob storage
  if (!envContent.includes('BLOB_READ_WRITE_TOKEN=')) {
    console.log('📁 File Storage');
    console.log('   Get Vercel Blob token at: https://vercel.com/docs/storage/vercel-blob');
    const blobToken = await question('   Enter BLOB_READ_WRITE_TOKEN: ');
    if (blobToken.trim()) {
      updateEnvVar('BLOB_READ_WRITE_TOKEN', blobToken.trim());
      console.log('   ✅ Blob storage configured\n');
    } else {
      console.log('   ⚠️  Blob storage is required for file uploads\n');
    }
  }

  // Write the updated .env.local file
  writeFileSync(envPath, envContent.trim() + '\n');
  console.log('💾 Configuration saved to .env.local\n');

  // Test the configuration
  console.log('🧪 Testing configuration...\n');
  
  rl.close();

  // Import and run the test
  try {
    const { exec } = require('child_process');
    exec('pnpm exec tsx scripts/test-providers.ts', (error: any, stdout: string, stderr: string) => {
      if (error) {
        console.error('❌ Test failed:', error);
        return;
      }
      console.log(stdout);
      
      console.log('🎉 Setup complete! You can now start the development server with:');
      console.log('   pnpm dev\n');
      console.log('📚 For more information, see docs/multi-provider-setup.md');
    });
  } catch (error) {
    console.log('✅ Configuration saved successfully!');
    console.log('\n🎉 Setup complete! Run the following to test your configuration:');
    console.log('   pnpm exec tsx scripts/test-providers.ts\n');
    console.log('📚 For more information, see docs/multi-provider-setup.md');
  }
}

main().catch(console.error);
