{"name": "AI Chatbot Design System", "version": "1.0.0", "defaultTheme": "light", "themes": [{"name": "light", "displayName": "Light", "description": "Clean and bright theme for daytime use"}, {"name": "dark", "displayName": "Dark", "description": "Easy on the eyes for low-light environments"}, {"name": "high-contrast", "displayName": "High Contrast", "description": "Enhanced contrast for better accessibility"}, {"name": "blue", "displayName": "Ocean Blue", "description": "Professional blue color scheme"}, {"name": "green", "displayName": "Forest Green", "description": "Natural green color palette"}, {"name": "purple", "displayName": "Royal Purple", "description": "Rich purple theme for creativity"}], "customizations": {"enableAnimations": true, "enableTransitions": true, "enableShadows": true, "compactMode": false, "highContrast": false, "reducedMotion": false}, "typography": {"fontFamily": {"sans": ["var(--font-geist)", "system-ui", "sans-serif"], "mono": ["var(--font-geist-mono)", "monospace"]}, "scale": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem"}}, "spacing": {"scale": 1.0, "unit": "rem", "values": {"xs": "0.25", "sm": "0.5", "md": "1", "lg": "1.5", "xl": "2", "2xl": "3", "3xl": "4"}}, "components": {"button": {"variants": {"size": ["sm", "md", "lg"], "variant": ["default", "secondary", "outline", "ghost", "destructive"]}, "defaultProps": {"size": "md", "variant": "default"}}, "card": {"variants": {"variant": ["default", "outlined", "elevated"], "padding": ["sm", "md", "lg"]}, "defaultProps": {"variant": "default", "padding": "md"}}, "input": {"variants": {"size": ["sm", "md", "lg"], "variant": ["default", "filled", "outlined"]}, "defaultProps": {"size": "md", "variant": "default"}}}, "breakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}, "animations": {"duration": {"fast": "150ms", "normal": "300ms", "slow": "500ms"}, "easing": {"ease": "cubic-bezier(0.4, 0, 0.2, 1)", "easeIn": "cubic-bezier(0.4, 0, 1, 1)", "easeOut": "cubic-bezier(0, 0, 0.2, 1)", "easeInOut": "cubic-bezier(0.4, 0, 0.2, 1)"}}, "accessibility": {"focusRing": true, "reducedMotion": "respect-user-preference", "highContrast": "auto", "screenReaderSupport": true}, "features": {"darkMode": true, "themeCustomization": true, "responsiveDesign": true, "animationControls": true, "accessibilityFeatures": true}}